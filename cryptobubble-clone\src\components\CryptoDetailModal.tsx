"use client";

import React from "react";
import {
  X,
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  Volume2,
} from "lucide-react";
import { CryptoCurrency } from "@/types";
import { motion, AnimatePresence } from "framer-motion";

interface CryptoDetailModalProps {
  crypto: CryptoCurrency | null;
  isOpen: boolean;
  onClose: () => void;
}

export const CryptoDetailModal: React.FC<CryptoDetailModalProps> = ({
  crypto,
  isOpen,
  onClose,
}) => {
  if (!crypto) return null;

  const formatCurrency = (value: number) => {
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  const formatNumber = (value: number) => {
    return value.toLocaleString();
  };

  return (
    <AnimatePresence data-oid="dxsnpvm">
      {isOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center"
          data-oid="kjx7j-x"
        >
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
            data-oid="ft-nn_v"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
            data-oid="br73dzc"
          >
            {/* Header */}
            <div
              className="flex items-center justify-between p-6 border-b"
              data-oid="42qxy-c"
            >
              <div className="flex items-center gap-4" data-oid="lze03yr">
                <div
                  className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg"
                  data-oid="wu_vcr6"
                >
                  {crypto.symbol.charAt(0)}
                </div>
                <div data-oid="ldlglq-">
                  <h2
                    className="text-2xl font-bold text-gray-900"
                    data-oid="g1yb3h2"
                  >
                    {crypto.name}
                  </h2>
                  <p className="text-gray-500" data-oid=".jp34zt">
                    {crypto.symbol} • Rank #{crypto.rank}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                data-oid=".wc29:q"
              >
                <X className="w-6 h-6 text-gray-500" data-oid=":gy8:3i" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6" data-oid="swk:tev">
              {/* Price Section */}
              <div className="mb-8" data-oid="x90c37r">
                <div
                  className="flex items-center gap-4 mb-4"
                  data-oid="pq_xwji"
                >
                  <span
                    className="text-4xl font-bold text-gray-900"
                    data-oid="x._kmty"
                  >
                    ${formatNumber(crypto.price)}
                  </span>
                  <div
                    className={`flex items-center gap-1 px-3 py-1 rounded-full ${
                      crypto.change24h >= 0
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                    data-oid="bb7cml1"
                  >
                    {crypto.change24h >= 0 ? (
                      <TrendingUp className="w-4 h-4" data-oid="7ab2wly" />
                    ) : (
                      <TrendingDown className="w-4 h-4" data-oid="s_ui9t-" />
                    )}
                    <span className="font-semibold" data-oid="8u5zds6">
                      {crypto.change24h >= 0 ? "+" : ""}
                      {crypto.change24h.toFixed(2)}%
                    </span>
                  </div>
                </div>

                <div className="text-sm text-gray-600" data-oid="ie.1brh">
                  7d change:
                  <span
                    className={`ml-1 font-semibold ${
                      crypto.change7d >= 0 ? "text-green-600" : "text-red-600"
                    }`}
                    data-oid="6mve8sp"
                  >
                    {crypto.change7d >= 0 ? "+" : ""}
                    {crypto.change7d.toFixed(2)}%
                  </span>
                </div>
              </div>

              {/* Stats Grid */}
              <div
                className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"
                data-oid="i:i:mv1"
              >
                <div className="bg-gray-50 p-4 rounded-lg" data-oid="-gov4su">
                  <div
                    className="flex items-center gap-2 mb-2"
                    data-oid="r2:c.zl"
                  >
                    <BarChart3
                      className="w-5 h-5 text-blue-500"
                      data-oid="gtwkr.o"
                    />

                    <span
                      className="font-semibold text-gray-700"
                      data-oid="ine-s5n"
                    >
                      Market Cap
                    </span>
                  </div>
                  <p
                    className="text-2xl font-bold text-gray-900"
                    data-oid="8kpvxmu"
                  >
                    {formatCurrency(crypto.marketCap)}
                  </p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg" data-oid="ivibyej">
                  <div
                    className="flex items-center gap-2 mb-2"
                    data-oid=".2dh-_-"
                  >
                    <Volume2
                      className="w-5 h-5 text-purple-500"
                      data-oid="9_a1pib"
                    />

                    <span
                      className="font-semibold text-gray-700"
                      data-oid="0_:03pj"
                    >
                      24h Volume
                    </span>
                  </div>
                  <p
                    className="text-2xl font-bold text-gray-900"
                    data-oid="0tanxyf"
                  >
                    {formatCurrency(crypto.volume24h)}
                  </p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg" data-oid="wqt9sua">
                  <div
                    className="flex items-center gap-2 mb-2"
                    data-oid="i2bg_gm"
                  >
                    <DollarSign
                      className="w-5 h-5 text-green-500"
                      data-oid="356mp4t"
                    />

                    <span
                      className="font-semibold text-gray-700"
                      data-oid="4tejedu"
                    >
                      Category
                    </span>
                  </div>
                  <p
                    className="text-xl font-bold text-gray-900"
                    data-oid="dfdsxfe"
                  >
                    {crypto.category}
                  </p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg" data-oid="827hbsi">
                  <div
                    className="flex items-center gap-2 mb-2"
                    data-oid="u670c94"
                  >
                    <TrendingUp
                      className="w-5 h-5 text-orange-500"
                      data-oid="hr1-swv"
                    />

                    <span
                      className="font-semibold text-gray-700"
                      data-oid="rxlujaw"
                    >
                      Volume/MCap
                    </span>
                  </div>
                  <p
                    className="text-xl font-bold text-gray-900"
                    data-oid="58e7p21"
                  >
                    {((crypto.volume24h / crypto.marketCap) * 100).toFixed(2)}%
                  </p>
                </div>
              </div>

              {/* Description */}
              {crypto.description && (
                <div className="mb-6" data-oid="4lh4opq">
                  <h3
                    className="text-lg font-semibold text-gray-900 mb-2"
                    data-oid="g-f88hd"
                  >
                    About
                  </h3>
                  <p
                    className="text-gray-600 leading-relaxed"
                    data-oid="o9qh89v"
                  >
                    {crypto.description}
                  </p>
                </div>
              )}

              {/* Mock Chart Placeholder */}
              <div
                className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg"
                data-oid="d7k_qd1"
              >
                <h3
                  className="text-lg font-semibold text-gray-900 mb-4"
                  data-oid="9jsokkc"
                >
                  Price Chart (7 days)
                </h3>
                <div
                  className="h-32 bg-white rounded border-2 border-dashed border-gray-300 flex items-center justify-center"
                  data-oid="ss.1vtk"
                >
                  <p className="text-gray-500" data-oid="0ncg0fk">
                    Chart visualization would go here
                  </p>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div
              className="p-6 border-t bg-gray-50 rounded-b-xl"
              data-oid=".3jogse"
            >
              <div
                className="flex justify-between items-center text-sm text-gray-600"
                data-oid="osf4ld1"
              >
                <span data-oid="wvqq4-h">Last updated: Just now</span>
                <span data-oid="v07zde6">Data provided by CryptoBubble</span>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};
