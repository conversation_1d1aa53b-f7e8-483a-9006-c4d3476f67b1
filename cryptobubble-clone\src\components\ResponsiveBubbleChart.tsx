'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Bubble<PERSON>hart } from './BubbleChart';
import { BubbleChartSkeleton } from './LoadingSpinner';
import { CryptoCurrency } from '@/types';

interface ResponsiveBubbleChartProps {
  data: CryptoCurrency[];
  onBubbleClick?: (crypto: CryptoCurrency) => void;
  onBubbleHover?: (crypto: CryptoCurrency | null) => void;
  isLoading?: boolean;
}

export const ResponsiveBubbleChart: React.FC<ResponsiveBubbleChartProps> = ({
  data,
  onBubbleClick,
  onBubbleHover,
  isLoading = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 1200, height: 700 });

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width } = containerRef.current.getBoundingClientRect();
        const height = Math.max(500, Math.min(800, width * 0.6)); // Maintain aspect ratio
        setDimensions({ width: width - 48, height }); // Account for padding
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  if (isLoading) {
    return <BubbleChartSkeleton />;
  }

  return (
    <div ref={containerRef} className="w-full">
      <BubbleChart
        data={data}
        width={dimensions.width}
        height={dimensions.height}
        onBubbleClick={onBubbleClick}
        onBubbleHover={onBubbleHover}
      />
    </div>
  );
};
