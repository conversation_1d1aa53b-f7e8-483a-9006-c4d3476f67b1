"use client";

import React, { useState, useEffect, useRef } from "react";
import { Bubble<PERSON>hart } from "./BubbleC<PERSON>";
import { BubbleChartSkeleton } from "./LoadingSpinner";
import { CryptoCurrency } from "@/types";

interface ResponsiveBubbleChartProps {
  data: CryptoCurrency[];
  onBubbleClick?: (crypto: CryptoCurrency) => void;
  onBubbleHover?: (crypto: CryptoCurrency | null) => void;
  isLoading?: boolean;
}

export const ResponsiveBubbleChart: React.FC<ResponsiveBubbleChartProps> = ({
  data,
  onBubbleClick,
  onBubbleHover,
  isLoading = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 1920,
    height: typeof window !== "undefined" ? window.innerHeight : 1080,
  });

  useEffect(() => {
    const updateDimensions = () => {
      // Use full viewport dimensions
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateDimensions();
    window.addEventListener("resize", updateDimensions);

    return () => window.removeEventListener("resize", updateDimensions);
  }, []);

  if (isLoading) {
    return <BubbleChartSkeleton />;
  }

  return (
    <div ref={containerRef} className="fixed inset-0 w-full h-full">
      <BubbleChart
        data={data}
        width={dimensions.width}
        height={dimensions.height}
        onBubbleClick={onBubbleClick}
        onBubbleHover={onBubbleHover}
      />
    </div>
  );
};
