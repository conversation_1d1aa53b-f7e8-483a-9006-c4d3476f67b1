"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[415],{760:(t,e,n)=>{n.d(e,{N:()=>g});var i=n(5155),r=n(2115),s=n(869),o=n(2885),a=n(7494),l=n(845),u=n(7351),h=n(1508);class c extends r.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,n=(0,u.s)(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f(t){let{children:e,isPresent:n,anchorX:s}=t,o=(0,r.useId)(),a=(0,r.useRef)(null),l=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,r.useContext)(h.Q);return(0,r.useInsertionEffect)(()=>{let{width:t,height:e,top:i,left:r,right:h}=l.current;if(n||!a.current||!t||!e)return;a.current.dataset.motionPopId=o;let c=document.createElement("style");return u&&(c.nonce=u),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(r):"right: ".concat(h),"px !important;\n            top: ").concat(i,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[n]),(0,i.jsx)(c,{isPresent:n,childRef:a,sizeRef:l,children:r.cloneElement(e,{ref:a})})}let d=t=>{let{children:e,initial:n,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:h,mode:c,anchorX:d}=t,m=(0,o.M)(p),v=(0,r.useId)(),y=!0,g=(0,r.useMemo)(()=>(y=!1,{id:v,initial:n,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(m.set(t,!0),m.values()))if(!e)return;a&&a()},register:t=>(m.set(t,!1),()=>m.delete(t))}),[s,m,a]);return h&&y&&(g={...g}),(0,r.useMemo)(()=>{m.forEach((t,e)=>m.set(e,!1))},[s]),r.useEffect(()=>{s||m.size||!a||a()},[s]),"popLayout"===c&&(e=(0,i.jsx)(f,{isPresent:s,anchorX:d,children:e})),(0,i.jsx)(l.t.Provider,{value:g,children:e})};function p(){return new Map}var m=n(2082);let v=t=>t.key||"";function y(t){let e=[];return r.Children.forEach(t,t=>{(0,r.isValidElement)(t)&&e.push(t)}),e}let g=t=>{let{children:e,custom:n,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:c="sync",propagate:f=!1,anchorX:p="left"}=t,[g,x]=(0,m.xQ)(f),w=(0,r.useMemo)(()=>y(e),[e]),b=f&&!g?[]:w.map(v),_=(0,r.useRef)(!0),M=(0,r.useRef)(w),A=(0,o.M)(()=>new Map),[T,S]=(0,r.useState)(w),[P,k]=(0,r.useState)(w);(0,a.E)(()=>{_.current=!1,M.current=w;for(let t=0;t<P.length;t++){let e=v(P[t]);b.includes(e)?A.delete(e):!0!==A.get(e)&&A.set(e,!1)}},[P,b.length,b.join("-")]);let E=[];if(w!==T){let t=[...w];for(let e=0;e<P.length;e++){let n=P[e],i=v(n);b.includes(i)||(t.splice(e,0,n),E.push(n))}return"wait"===c&&E.length&&(t=E),k(y(t)),S(w),null}let{forceRender:V}=(0,r.useContext)(s.L);return(0,i.jsx)(i.Fragment,{children:P.map(t=>{let e=v(t),r=(!f||!!g)&&(w===P||b.includes(e));return(0,i.jsx)(d,{isPresent:r,initial:(!_.current||!!l)&&void 0,custom:n,presenceAffectsLayout:h,mode:c,onExitComplete:r?void 0:()=>{if(!A.has(e))return;A.set(e,!0);let t=!0;A.forEach(e=>{e||(t=!1)}),t&&(null==V||V(),k(M.current),f&&(null==x||x()),u&&u())},anchorX:p,children:t},e)})})}},845:(t,e,n)=>{n.d(e,{t:()=>i});let i=(0,n(2115).createContext)(null)},869:(t,e,n)=>{n.d(e,{L:()=>i});let i=(0,n(2115).createContext)({})},1508:(t,e,n)=>{n.d(e,{Q:()=>i});let i=(0,n(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,n)=>{n.d(e,{xQ:()=>s});var i=n(2115),r=n(845);function s(t=!0){let e=(0,i.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:n,onExitComplete:o,register:a}=e,l=(0,i.useId)();(0,i.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,i.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!n&&o?[!1,u]:[!0]}},2713:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2885:(t,e,n)=>{n.d(e,{M:()=>r});var i=n(2115);function r(t){let e=(0,i.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3109:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4416:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5273:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(9946).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},5868:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6408:(t,e,n)=>{let i;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function o(t,e,n,i){if("function"==typeof e){let[r,o]=s(i);e=e(void 0!==n?n:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(i);e=e(void 0!==n?n:t.custom,r,o)}return e}function a(t,e,n){let i=t.getProps();return o(i,e,void 0!==n?n:i.custom,t)}function l(t,e){return t?.[e]??t?.default??t}n.d(e,{P:()=>sP});let u=t=>t,h={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],f={value:null,addProjectionMetrics:null};function d(t,e){let n=!1,i=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,o=c.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?n:i;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[n,i]=[i,n],n.forEach(u),e&&f.value&&f.value.frameloop[e].push(l),l=0,n.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?n:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:d,update:p,preRender:m,render:v,postRender:y}=o,g=()=>{let s=h.useManualTiming?r.timestamp:performance.now();n=!1,h.useManualTiming||(r.delta=i?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),d.process(r),p.process(r),m.process(r),v.process(r),y.process(r),r.isProcessing=!1,n&&e&&(i=!1,t(g))},x=()=>{n=!0,i=!0,r.isProcessing||t(g)};return{schedule:c.reduce((t,e)=>{let i=o[e];return t[e]=(t,e=!1,r=!1)=>(n||x(),i.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)o[c[e]].cancel(t)},state:r,steps:o}}let{schedule:p,cancel:m,state:v,steps:y}=d("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),g=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(g),w=new Set(["width","height","top","left","right","bottom",...g]);function b(t,e){-1===t.indexOf(e)&&t.push(e)}function _(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}class M{constructor(){this.subscriptions=[]}add(t){return b(this.subscriptions,t),()=>_(this.subscriptions,t)}notify(t,e,n){let i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let r=0;r<i;r++){let i=this.subscriptions[r];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function A(){i=void 0}let T={now:()=>(void 0===i&&T.set(v.isProcessing||h.useManualTiming?v.timestamp:performance.now()),i),set:t=>{i=t,queueMicrotask(A)}},S=t=>!isNaN(parseFloat(t)),P={current:void 0};class k{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let n=T.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=T.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new M);let n=this.events[t].add(e);return"change"===t?()=>{n(),p.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return P.current&&P.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=T.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function E(t,e){return new k(t,e)}let V=t=>Array.isArray(t),C=t=>!!(t&&t.getVelocity);function D(t,e){let n=t.getValue("willChange");if(C(n)&&n.add)return n.add(e);if(!n&&h.WillChange){let n=new h.WillChange("auto");t.addValue("willChange",n),n.add(e)}}let N=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),R="data-"+N("framerAppearId"),j=(t,e)=>n=>e(t(n)),L=(...t)=>t.reduce(j),B=(t,e,n)=>n>e?e:n<t?t:n,F=t=>1e3*t,O=t=>t/1e3,z={layout:0,mainThread:0,waapi:0},$=()=>{},I=()=>{},U=t=>e=>"string"==typeof e&&e.startsWith(t),q=U("--"),X=U("var(--"),W=t=>!!X(t)&&Y.test(t.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,H={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},K={...H,transform:t=>B(0,1,t)},G={...H,default:1},Z=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(t)||e&&null!=n&&Object.prototype.hasOwnProperty.call(n,e)),te=(t,e,n)=>i=>{if("string"!=typeof i)return i;let[r,s,o,a]=i.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[n]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tn=t=>B(0,255,t),ti={...H,transform:t=>Math.round(tn(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+ti.transform(t)+", "+ti.transform(e)+", "+ti.transform(n)+", "+Z(K.transform(i))+")"},ts={test:tt("#"),parse:function(t){let e="",n="",i="",r="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),r=t.substring(4,5),e+=e,n+=n,i+=i,r+=r),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),tc=to("vw"),tf={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},td={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(n))+", "+Z(K.transform(i))+")"},tp={test:t=>tr.test(t)||ts.test(t)||td.test(t),parse:t=>tr.test(t)?tr.parse(t):td.test(t)?td.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):td.transform(t),getAnimatableNone:t=>{let e=tp.parse(t);return e.alpha=0,tp.transform(e)}},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tv="number",ty="color",tg=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),n=[],i={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tg,t=>(tp.test(t)?(i.color.push(s),r.push(ty),n.push(tp.parse(t))):t.startsWith("var(")?(i.var.push(s),r.push("var"),n.push(t)):(i.number.push(s),r.push(tv),n.push(parseFloat(t))),++s,"${}")).split("${}");return{values:n,split:o,indexes:i,types:r}}function tw(t){return tx(t).values}function tb(t){let{split:e,types:n}=tx(t),i=e.length;return t=>{let r="";for(let s=0;s<i;s++)if(r+=e[s],void 0!==t[s]){let e=n[s];e===tv?r+=Z(t[s]):e===ty?r+=tp.transform(t[s]):r+=t[s]}return r}}let t_=t=>"number"==typeof t?0:tp.test(t)?tp.getAnimatableNone(t):t,tM={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tm)?.length||0)>0},parse:tw,createTransformer:tb,getAnimatableNone:function(t){let e=tw(t);return tb(t)(e.map(t_))}};function tA(t,e,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?t+(e-t)*6*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function tT(t,e){return n=>n>0?e:t}let tS=(t,e,n)=>t+(e-t)*n,tP=(t,e,n)=>{let i=t*t,r=n*(e*e-i)+i;return r<0?0:Math.sqrt(r)},tk=[ts,tr,td],tE=t=>tk.find(e=>e.test(t));function tV(t){let e=tE(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let n=e.parse(t);return e===td&&(n=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let r=0,s=0,o=0;if(e/=100){let i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;r=tA(a,i,t+1/3),s=tA(a,i,t),o=tA(a,i,t-1/3)}else r=s=o=n;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:i}}(n)),n}let tC=(t,e)=>{let n=tV(t),i=tV(e);if(!n||!i)return tT(t,e);let r={...n};return t=>(r.red=tP(n.red,i.red,t),r.green=tP(n.green,i.green,t),r.blue=tP(n.blue,i.blue,t),r.alpha=tS(n.alpha,i.alpha,t),tr.transform(r))},tD=new Set(["none","hidden"]);function tN(t,e){return n=>tS(t,e,n)}function tR(t){return"number"==typeof t?tN:"string"==typeof t?W(t)?tT:tp.test(t)?tC:tB:Array.isArray(t)?tj:"object"==typeof t?tp.test(t)?tC:tL:tT}function tj(t,e){let n=[...t],i=n.length,r=t.map((t,n)=>tR(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=r[e](t);return n}}function tL(t,e){let n={...t,...e},i={};for(let r in n)void 0!==t[r]&&void 0!==e[r]&&(i[r]=tR(t[r])(t[r],e[r]));return t=>{for(let e in i)n[e]=i[e](t);return n}}let tB=(t,e)=>{let n=tM.createTransformer(e),i=tx(t),r=tx(e);return i.indexes.var.length===r.indexes.var.length&&i.indexes.color.length===r.indexes.color.length&&i.indexes.number.length>=r.indexes.number.length?tD.has(t)&&!r.values.length||tD.has(e)&&!i.values.length?function(t,e){return tD.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):L(tj(function(t,e){let n=[],i={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][i[s]],a=t.values[o]??0;n[r]=a,i[s]++}return n}(i,r),r.values),n):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tT(t,e))};function tF(t,e,n){return"number"==typeof t&&"number"==typeof e&&"number"==typeof n?tS(t,e,n):tR(t)(t,e)}let tO=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>p.update(e,t),stop:()=>m(e),now:()=>v.isProcessing?v.timestamp:T.now()}},tz=(t,e,n=10)=>{let i="",r=Math.max(Math.round(e/n),2);for(let e=0;e<r;e++)i+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`};function t$(t){let e=0,n=t.next(e);for(;!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}function tI(t,e,n){var i,r;let s=Math.max(e-5,0);return i=n-t(s),(r=e-s)?1e3/r*i:0}let tU={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tq(t,e){return t*Math.sqrt(1-e*e)}let tX=["duration","bounce"],tW=["stiffness","damping","mass"];function tY(t,e){return e.some(e=>void 0!==t[e])}function tH(t=tU.visualDuration,e=tU.bounce){let n,i="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=i,o=i.keyframes[0],a=i.keyframes[i.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:f,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:tU.velocity,stiffness:tU.stiffness,damping:tU.damping,mass:tU.mass,isResolvedFromDuration:!1,...t};if(!tY(t,tW)&&tY(t,tX))if(t.visualDuration){let n=2*Math.PI/(1.2*t.visualDuration),i=n*n,r=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:tU.mass,stiffness:i,damping:r}}else{let n=function({duration:t=tU.duration,bounce:e=tU.bounce,velocity:n=tU.velocity,mass:i=tU.mass}){let r,s;$(t<=F(tU.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=B(tU.minDamping,tU.maxDamping,o),t=B(tU.minDuration,tU.maxDuration,O(t)),o<1?(r=e=>{let i=e*o,r=i*t;return .001-(i-n)/tq(e,o)*Math.exp(-r)},s=e=>{let i=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-i),l=tq(Math.pow(e,2),o);return(i*n+n-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-n)*t+1),s=e=>t*t*(n-e)*Math.exp(-e*t));let a=function(t,e,n){let i=n;for(let n=1;n<12;n++)i-=t(i)/e(i);return i}(r,s,5/t);if(t=F(t),isNaN(a))return{stiffness:tU.stiffness,damping:tU.damping,duration:t};{let e=Math.pow(a,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}(t);(e={...e,...n,mass:tU.mass}).isResolvedFromDuration=!0}return e}({...i,velocity:-O(i.velocity||0)}),m=d||0,v=h/(2*Math.sqrt(u*c)),y=a-o,g=O(Math.sqrt(u/c)),x=5>Math.abs(y);if(r||(r=x?tU.restSpeed.granular:tU.restSpeed.default),s||(s=x?tU.restDelta.granular:tU.restDelta.default),v<1){let t=tq(g,v);n=e=>a-Math.exp(-v*g*e)*((m+v*g*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===v)n=t=>a-Math.exp(-g*t)*(y+(m+g*y)*t);else{let t=g*Math.sqrt(v*v-1);n=e=>{let n=Math.exp(-v*g*e),i=Math.min(t*e,300);return a-n*((m+v*g*y)*Math.sinh(i)+t*y*Math.cosh(i))/t}}let w={calculatedDuration:p&&f||null,next:t=>{let e=n(t);if(p)l.done=t>=f;else{let i=0===t?m:0;v<1&&(i=0===t?F(m):tI(n,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(i)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(t$(w),2e4),e=tz(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tK({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,f,d=t[0],p={done:!1,value:d},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,y=n*e,g=d+y,x=void 0===o?g:o(g);x!==g&&(y=x-d);let w=t=>-y*Math.exp(-t/i),b=t=>x+w(t),_=t=>{let e=w(t),n=b(t);p.done=Math.abs(e)<=u,p.value=p.done?x:n},M=t=>{m(p.value)&&(c=t,f=tH({keyframes:[p.value,v(p.value)],velocity:tI(b,t,p.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return(f||void 0!==c||(e=!0,_(t),M(t)),void 0!==c&&t>=c)?f.next(t-c):(e||_(t),p)}}}tH.applyToOptions=t=>{let e=function(t,e=100,n){let i=n({...t,keyframes:[0,e]}),r=Math.min(t$(i),2e4);return{type:"keyframes",ease:t=>i.next(r*t).value/e,duration:O(r)}}(t,100,tH);return t.ease=e.ease,t.duration=F(e.duration),t.type="keyframes",t};let tG=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function tZ(t,e,n,i){if(t===e&&n===i)return u;let r=e=>(function(t,e,n,i,r){let s,o,a=0;do(s=tG(o=e+(n-e)/2,i,r)-t)>0?n=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,n);return t=>0===t||1===t?t:tG(r(t),e,i)}let tQ=tZ(.42,0,1,1),tJ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t3=tZ(.33,1.53,.69,.99),t4=t5(t3),t8=t2(t4),t9=t=>(t*=2)<1?.5*t4(t):.5*(2-Math.pow(2,-10*(t-1))),t6=t=>1-Math.sin(Math.acos(t)),t7=t5(t6),et=t2(t6),ee=t=>Array.isArray(t)&&"number"==typeof t[0],en={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t6,circInOut:et,circOut:t7,backIn:t4,backInOut:t8,backOut:t3,anticipate:t9},ei=t=>"string"==typeof t,er=t=>{if(ee(t)){I(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,n,i,r]=t;return tZ(e,n,i,r)}return ei(t)?(I(void 0!==en[t],`Invalid easing type '${t}'`),en[t]):t},es=(t,e,n)=>{let i=e-t;return 0===i?1:(n-t)/i};function eo({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){var r;let s=t1(i)?i.map(er):er(i),o={done:!1,value:e[0]},a=function(t,e,{clamp:n=!0,ease:i,mixer:r}={}){let s=t.length;if(I(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,n){let i=[],r=n||h.mix||tF,s=t.length-1;for(let n=0;n<s;n++){let s=r(t[n],t[n+1]);e&&(s=L(Array.isArray(e)?e[n]||u:e,s)),i.push(s)}return i}(e,i,r),l=a.length,c=n=>{if(o&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);let r=es(t[i],t[i+1],n);return a[i](r)};return n?e=>c(B(t[0],t[s-1],e)):c}((r=n&&n.length===e.length?n:function(t){let e=[0];return!function(t,e){let n=t[t.length-1];for(let i=1;i<=e;i++){let r=es(0,e,i);t.push(tS(n,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:n="loop"},i,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}let eu={decay:tK,inertia:tK,tween:eo,keyframes:eo,spring:tH};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ef=t=>t/100;class ed extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==T.now()&&this.tick(T.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},z.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:n=0,repeatDelay:i=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=L(ef,tF(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=t$(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:n,totalDuration:i,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:f,type:d,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let v=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?v<0:v>i;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let g=this.currentTime,x=n;if(h){let t=Math.min(this.currentTime,i)/o,e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(n=1-n,f&&(n-=f/o)):"mirror"===c&&(x=s)),g=B(0,1,n)*o}let w=y?{done:!1,value:u[0]}:x.next(g);r&&(w.value=r(w.value));let{done:b}=w;y||null===a||(b=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);let _=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return _&&d!==tK&&(w.value=el(u,this.options,m,this.speed)),p&&p(w.value),_&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return O(this.calculatedDuration)}get time(){return O(this.currentTime)}set time(t){t=F(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(T.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=O(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tO,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(T.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,z.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ep=t=>180*t/Math.PI,em=t=>ey(ep(Math.atan2(t[1],t[0]))),ev={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ep(Math.atan(t[1])),skewY:t=>ep(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),eg=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ew={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eg,scaleY:ex,scale:t=>(eg(t)+ex(t))/2,rotateX:t=>ey(ep(Math.atan2(t[6],t[5]))),rotateY:t=>ey(ep(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ep(Math.atan(t[4])),skewY:t=>ep(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eb(t){return+!!t.includes("scale")}function e_(t,e){let n,i;if(!t||"none"===t)return eb(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)n=ew,i=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=ev,i=e}if(!i)return eb(e);let s=n[e],o=i[1].split(",").map(eA);return"function"==typeof s?s(o):o[s]}let eM=(t,e)=>{let{transform:n="none"}=getComputedStyle(t);return e_(n,e)};function eA(t){return parseFloat(t.trim())}let eT=t=>t===H||t===tu,eS=new Set(["x","y","z"]),eP=g.filter(t=>!eS.has(t)),ek={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>e_(e,"x"),y:(t,{transform:e})=>e_(e,"y")};ek.translateX=ek.x,ek.translateY=ek.y;let eE=new Set,eV=!1,eC=!1,eD=!1;function eN(){if(eC){let t=Array.from(eE).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{let e=function(t){let e=[];return eP.forEach(n=>{let i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(+!!n.startsWith("scale")))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eC=!1,eV=!1,eE.forEach(t=>t.complete(eD)),eE.clear()}function eR(){eE.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eC=!0)})}class ej{constructor(t,e,n,i,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eE.add(this),eV||(eV=!0,p.read(eR),p.resolveKeyframes(eN))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){let r=i?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(n&&e){let i=n.readValue(e,s);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=s),i&&void 0===r&&i.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eE.delete(this)}cancel(){"scheduled"===this.state&&(eE.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eL=t=>t.startsWith("--");function eB(t){let e;return()=>(void 0===e&&(e=t()),e)}let eF=eB(()=>void 0!==window.ScrollTimeline),eO={},ez=function(t,e){let n=eB(t);return()=>eO[e]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),e$=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,eI={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e$([0,.65,.55,1]),circOut:e$([.55,0,1,.45]),backIn:e$([.31,.01,.66,-.59]),backOut:e$([.33,1.53,.69,.99])};function eU(t){return"function"==typeof t&&"applyToOptions"in t}class eq extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:n,keyframes:i,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,I("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eU(t)&&ez()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,n,{delay:i=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:n};l&&(h.offset=l);let c=function t(e,n){if(e)return"function"==typeof e?ez()?tz(e,n):"ease-out":ee(e)?e$(e):Array.isArray(e)?e.map(e=>t(e,n)||eI.easeOut):eI[e]}(a,r);Array.isArray(c)&&(h.easing=c),f.value&&z.waapi++;let d={delay:i,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);let p=t.animate(h,d);return f.value&&p.finished.finally(()=>{z.waapi--}),p}(e,n,i,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(i,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){eL(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return O(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return O(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=F(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eF())?(this.animation.timeline=t,u):e(this)}}let eX={anticipate:t9,backInOut:t8,circInOut:et};class eW extends eq{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eX&&(t.ease=eX[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:n,onComplete:i,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ed({...s,autoplay:!1}),a=F(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eY=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tM.test(t)||"0"===t)&&!t.startsWith("url("));var eH,eK,eG=n(7351);let eZ=new Set(["opacity","clipPath","filter","transform"]),eQ=eB(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ec{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=T.now();let c={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},f=u?.KeyframeResolver||ej;this.keyframeResolver=new f(o,(t,e,n)=>this.onKeyframesResolved(t,e,c,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:c}=n;this.resolvedAt=T.now(),!function(t,e,n,i){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eY(r,e),a=eY(s,e);return $(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||eU(n))&&i)}(t,r,s,o)&&((h.instantAnimations||!a)&&c?.(el(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);let f={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},d=!l&&function(t){let{motionValue:e,name:n,repeatDelay:i,repeatType:r,damping:s,type:o}=t;if(!(0,eG.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&n&&eZ.has(n)&&("transform"!==n||!l)&&!a&&!i&&"mirror"!==r&&0!==s&&"inertia"!==o}(f)?new eW({...f,element:f.motionValue.owner.current}):new ed(f);d.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eD=!0,eR(),eN(),eD=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e5={type:"keyframes",duration:.8},e3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e4=(t,{keyframes:e})=>e.length>2?e5:x.has(t)?t.startsWith("scale")?e2(e[1]):e1:e3,e8=(t,e,n,i={},r,s)=>o=>{let a=l(i,t)||{},u=a.delay||i.delay||0,{elapsed:c=0}=i;c-=F(u);let f={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(f,e4(t,f)),f.duration&&(f.duration=F(f.duration)),f.repeatDelay&&(f.repeatDelay=F(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);let d=!1;if(!1!==f.type&&(0!==f.duration||f.repeatDelay)||(f.duration=0,0===f.delay&&(d=!0)),(h.instantAnimations||h.skipAnimations)&&(d=!0,f.duration=0,f.delay=0),f.allowFlatten=!a.type&&!a.ease,d&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:n="loop"},i){let r=t.filter(e0),s=e&&"loop"!==n&&e%2==1?0:r.length-1;return r[s]}(f.keyframes,a);if(void 0!==t)return void p.update(()=>{f.onUpdate(t),f.onComplete()})}return a.isSync?new ed(f):new eJ(f)};function e9(t,e,{delay:n=0,transitionOverride:i,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;i&&(s=i);let h=[],c=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let i=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||c&&function({protectedKeys:t,needsAnimating:e},n){let i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}(c,e))continue;let o={delay:n,...l(s||{},e)},a=i.get();if(void 0!==a&&!i.isAnimating&&!Array.isArray(r)&&r===a&&!o.velocity)continue;let f=!1;if(window.MotionHandoffAnimation){let n=t.props[R];if(n){let t=window.MotionHandoffAnimation(n,e,p);null!==t&&(o.startTime=t,f=!0)}}D(t,e),i.start(e8(e,i,r,t.shouldReduceMotion&&w.has(e)?{type:!1}:o,t,f));let d=i.animation;d&&h.push(d)}return o&&Promise.all(h).then(()=>{p.update(()=>{o&&function(t,e){let{transitionEnd:n={},transition:i={},...r}=a(t,e)||{};for(let e in r={...r,...n}){var s;let n=V(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,E(n))}}(t,o)})}),h}function e6(t,e,n={}){let i=a(t,e,"exit"===n.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(r=n.transitionOverride);let s=i?()=>Promise.all(e9(t,i,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,n=0,i=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*i,l=1===r?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(e7).forEach((t,i)=>{t.notify("AnimationStart",e),o.push(e6(t,e,{...s,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+i,o,a,n)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),o(n.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function nt(t,e){if(!Array.isArray(e))return!1;let n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function ne(t){return"string"==typeof t||Array.isArray(t)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ni=["initial",...nn],nr=ni.length,ns=[...nn].reverse(),no=nn.length;function na(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:na(!0),whileInView:na(),whileHover:na(),whileTap:na(),whileDrag:na(),whileFocus:na(),exit:na()}}class nu{constructor(t){this.isMounted=!1,this.node=t}update(){}}class nh extends nu{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:n})=>(function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>e6(t,e,n)));else if("string"==typeof e)i=e6(t,e,n);else{let r="function"==typeof e?a(t,e,n.custom):e;i=Promise.all(e9(t,r,n))}return i.then(()=>{t.notify("AnimationComplete",e)})})(t,e,n))),n=nl(),i=!0,s=e=>(n,i)=>{let r=a(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...i}=r;n={...n,...i,...e}}return n};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let n=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(n.initial=e.props.initial),n}let n={};for(let t=0;t<nr;t++){let i=ni[t],r=e.props[i];(ne(r)||!1===r)&&(n[i]=r)}return n}(t.parent)||{},h=[],c=new Set,f={},d=1/0;for(let e=0;e<no;e++){var p,m;let a=ns[e],v=n[a],y=void 0!==l[a]?l[a]:u[a],g=ne(y),x=a===o?v.isActive:null;!1===x&&(d=e);let w=y===u[a]&&y!==l[a]&&g;if(w&&i&&t.manuallyAnimateOnMount&&(w=!1),v.protectedKeys={...f},!v.isActive&&null===x||!y&&!v.prevProp||r(y)||"boolean"==typeof y)continue;let b=(p=v.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!nt(m,p)),_=b||a===o&&v.isActive&&!w&&g||e>d&&g,M=!1,A=Array.isArray(y)?y:[y],T=A.reduce(s(a),{});!1===x&&(T={});let{prevResolvedValues:S={}}=v,P={...S,...T},k=e=>{_=!0,c.has(e)&&(M=!0,c.delete(e)),v.needsAnimating[e]=!0;let n=t.getValue(e);n&&(n.liveStyle=!1)};for(let t in P){let e=T[t],n=S[t];if(f.hasOwnProperty(t))continue;let i=!1;(V(e)&&V(n)?nt(e,n):e===n)?void 0!==e&&c.has(t)?k(t):v.protectedKeys[t]=!0:null!=e?k(t):c.add(t)}v.prevProp=y,v.prevResolvedValues=T,v.isActive&&(f={...f,...T}),i&&t.blockInitialAnimation&&(_=!1);let E=!(w&&b)||M;_&&E&&h.push(...A.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let n=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(e.transition=n.transition)}c.forEach(n=>{let i=t.getBaseTarget(n),r=t.getValue(n);r&&(r.liveStyle=!0),e[n]=i??null}),h.push({animation:e})}let v=!!h.length;return i&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(v=!1),i=!1,v?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,i)),n[e].isActive=i;let r=o(e);for(let t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=nl(),i=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nc=0;class nf extends nu{constructor(){super(...arguments),this.id=nc++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;let i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let nd={x:!1,y:!1};function np(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}let nm=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function nv(t){return{point:{x:t.pageX,y:t.pageY}}}let ny=t=>e=>nm(e)&&t(e,nv(e));function ng(t,e,n,i){return np(t,e,ny(n),i)}function nx({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function nw(t){return t.max-t.min}function nb(t,e,n,i=.5){t.origin=i,t.originPoint=tS(e.min,e.max,t.origin),t.scale=nw(n)/nw(e),t.translate=tS(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function n_(t,e,n,i){nb(t.x,e.x,n.x,i?i.originX:void 0),nb(t.y,e.y,n.y,i?i.originY:void 0)}function nM(t,e,n){t.min=n.min+e.min,t.max=t.min+nw(e)}function nA(t,e,n){t.min=e.min-n.min,t.max=t.min+nw(e)}function nT(t,e,n){nA(t.x,e.x,n.x),nA(t.y,e.y,n.y)}let nS=()=>({translate:0,scale:1,origin:0,originPoint:0}),nP=()=>({x:nS(),y:nS()}),nk=()=>({min:0,max:0}),nE=()=>({x:nk(),y:nk()});function nV(t){return[t("x"),t("y")]}function nC(t){return void 0===t||1===t}function nD({scale:t,scaleX:e,scaleY:n}){return!nC(t)||!nC(e)||!nC(n)}function nN(t){return nD(t)||nR(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function nR(t){var e,n;return(e=t.x)&&"0%"!==e||(n=t.y)&&"0%"!==n}function nj(t,e,n,i,r){return void 0!==r&&(t=i+r*(t-i)),i+n*(t-i)+e}function nL(t,e=0,n=1,i,r){t.min=nj(t.min,e,n,i,r),t.max=nj(t.max,e,n,i,r)}function nB(t,{x:e,y:n}){nL(t.x,e.translate,e.scale,e.originPoint),nL(t.y,n.translate,n.scale,n.originPoint)}function nF(t,e){t.min=t.min+e,t.max=t.max+e}function nO(t,e,n,i,r=.5){let s=tS(t.min,t.max,r);nL(t,e,n,s,i)}function nz(t,e){nO(t.x,e.x,e.scaleX,e.scale,e.originX),nO(t.y,e.y,e.scaleY,e.scale,e.originY)}function n$(t,e){return nx(function(t,e){if(!e)return t;let n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}let nI=({current:t})=>t?t.ownerDocument.defaultView:null;function nU(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let nq=(t,e)=>Math.abs(t-e);class nX{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=nH(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){return Math.sqrt(nq(t.x,e.x)**2+nq(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;let{point:i}=t,{timestamp:r}=v;this.history.push({...i,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=nW(e,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:n,onSessionEnd:i,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=nH("pointercancel"===t.type?this.lastMoveEventInfo:nW(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,s),i&&i(t,s)},!nm(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;let s=nW(nv(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=v;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,nH(s,this.history)),this.removeListeners=L(ng(this.contextWindow,"pointermove",this.handlePointerMove),ng(this.contextWindow,"pointerup",this.handlePointerUp),ng(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function nW(t,e){return e?{point:e(t.point)}:t}function nY(t,e){return{x:t.x-e.x,y:t.y-e.y}}function nH({point:t},e){return{point:t,delta:nY(t,nK(e)),offset:nY(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null,r=nK(t);for(;n>=0&&(i=t[n],!(r.timestamp-i.timestamp>F(.1)));)n--;if(!i)return{x:0,y:0};let s=O(r.timestamp-i.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-i.x)/s,y:(r.y-i.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function nK(t){return t[t.length-1]}function nG(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function nZ(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function nQ(t,e,n){return{min:nJ(t,e),max:nJ(t,n)}}function nJ(t,e){return"number"==typeof t?t:t[e]||0}let n0=new WeakMap;class n1{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nE(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new nX(t,{onSessionStart:t=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(nv(t).point)},onStart:(t,e)=>{let{drag:n,dragPropagation:i,onDragStart:r}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(nd[t])return null;else return nd[t]=!0,()=>{nd[t]=!1};return nd.x||nd.y?null:(nd.x=nd.y=!0,()=>{nd.x=nd.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nV(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:n}=this.visualElement;if(n&&n.layout){let i=n.layout.layoutBox[t];i&&(e=nw(i)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&p.postRender(()=>r(t,e)),D(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:n,dragDirectionLock:i,onDirectionLock:r,onDrag:s}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:o}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>nV(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:nI(this.visualElement)})}stop(t,e){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:r}=this.getProps();r&&p.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){let{drag:i}=this.getProps();if(!n||!n2(t,i,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?tS(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?tS(n,t,i.max):Math.min(t,n)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&nU(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(t,{top:e,left:n,bottom:i,right:r}){return{x:nG(t.x,n,r),y:nG(t.y,e,i)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:nQ(t,"left","right"),y:nQ(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nV(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!nU(e))return!1;let i=e.current;I(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,n){let i=n$(t,n),{scroll:r}=e;return r&&(nF(i.x,r.offset.x),nF(i.y,r.offset.y)),i}(i,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:nZ(t.x,s.x),y:nZ(t.y,s.y)});if(n){let t=n(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=nx(t))}return o}startAnimation(t){let{drag:e,dragMomentum:n,dragElastic:i,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(nV(o=>{if(!n2(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:n?t[o]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let n=this.getAxisMotionValue(t);return D(this.visualElement,t),n.start(e8(t,n,0,e,this.visualElement,!1))}stopAnimation(){nV(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){nV(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps();return n[e]||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){nV(e=>{let{drag:n}=this.getProps();if(!n2(e,n,this.currentDirection))return;let{projection:i}=this.visualElement,r=this.getAxisMotionValue(e);if(i&&i.layout){let{min:n,max:s}=i.layout.layoutBox[e];r.set(t[e]-tS(n,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!nU(e)||!n||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};nV(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let n=e.get();i[t]=function(t,e){let n=.5,i=nw(t),r=nw(e);return r>i?n=es(e.min,e.max-i,t.min):i>r&&(n=es(t.min,t.max-r,e.min)),B(0,1,n)}({min:n,max:n},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nV(e=>{if(!n2(e,t,null))return;let n=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];n.set(tS(r,s,i[e]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let t=ng(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();nU(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),p.read(e);let r=np(window,"resize",()=>this.scalePositionWithinConstraints()),s=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(nV(e=>{let n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),i(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function n2(t,e,n){return(!0===e||e===t)&&(null===n||n===t)}class n5 extends nu{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n3=t=>(e,n)=>{t&&p.postRender(()=>t(e,n))};class n4 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new nX(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nI(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:n3(t),onStart:n3(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&p.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=ng(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n8=n(5155);let{schedule:n9}=d(queueMicrotask,!1);var n6=n(2115),n7=n(2082),it=n(869);let ie=(0,n6.createContext)({}),ii={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ir(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let is={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let n=ir(t,e.target.x),i=ir(t,e.target.y);return`${n}% ${i}%`}},io={};class ia extends n6.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:r}=t;for(let t in iu)io[t]=iu[t],q(t)&&(io[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),n&&n.register&&i&&n.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),ii.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:n,drag:i,isPresent:r}=this.props,{projection:s}=n;return s&&(s.isPresent=r,i||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||p.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),n9.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function il(t){let[e,n]=(0,n7.xQ)(),i=(0,n6.useContext)(it.L);return(0,n8.jsx)(ia,{...t,layoutGroup:i,switchLayoutGroup:(0,n6.useContext)(ie),isPresent:e,safeToRemove:n})}let iu={borderRadius:{...is,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:is,borderTopRightRadius:is,borderBottomLeftRadius:is,borderBottomRightRadius:is,boxShadow:{correct:(t,{treeScale:e,projectionDelta:n})=>{let i=tM.parse(t);if(i.length>5)return t;let r=tM.createTransformer(t),s=+("number"!=typeof i[0]),o=n.x.scale*e.x,a=n.y.scale*e.y;i[0+s]/=o,i[1+s]/=a;let l=tS(o,a,.5);return"number"==typeof i[2+s]&&(i[2+s]/=l),"number"==typeof i[3+s]&&(i[3+s]/=l),r(i)}}};var ih=n(6983);function ic(t){return(0,ih.G)(t)&&"ownerSVGElement"in t}let id=(t,e)=>t.depth-e.depth;class ip{constructor(){this.children=[],this.isDirty=!1}add(t){b(this.children,t),this.isDirty=!0}remove(t){_(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(id),this.isDirty=!1,this.children.forEach(t)}}function im(t){return C(t)?t.get():t}let iv=["TopLeft","TopRight","BottomLeft","BottomRight"],iy=iv.length,ig=t=>"string"==typeof t?parseFloat(t):t,ix=t=>"number"==typeof t||tu.test(t);function iw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let ib=iM(0,.5,t7),i_=iM(.5,.95,u);function iM(t,e,n){return i=>i<t?0:i>e?1:n(es(t,e,i))}function iA(t,e){t.min=e.min,t.max=e.max}function iT(t,e){iA(t.x,e.x),iA(t.y,e.y)}function iS(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function iP(t,e,n,i,r){return t-=e,t=i+1/n*(t-i),void 0!==r&&(t=i+1/r*(t-i)),t}function ik(t,e,[n,i,r],s,o){!function(t,e=0,n=1,i=.5,r,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tS(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tS(s.min,s.max,i);t===s&&(a-=e),t.min=iP(t.min,e,n,a,r),t.max=iP(t.max,e,n,a,r)}(t,e[n],e[i],e[r],e.scale,s,o)}let iE=["x","scaleX","originX"],iV=["y","scaleY","originY"];function iC(t,e,n,i){ik(t.x,e,iE,n?n.x:void 0,i?i.x:void 0),ik(t.y,e,iV,n?n.y:void 0,i?i.y:void 0)}function iD(t){return 0===t.translate&&1===t.scale}function iN(t){return iD(t.x)&&iD(t.y)}function iR(t,e){return t.min===e.min&&t.max===e.max}function ij(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function iL(t,e){return ij(t.x,e.x)&&ij(t.y,e.y)}function iB(t){return nw(t.x)/nw(t.y)}function iF(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class iO{constructor(){this.members=[]}add(t){b(this.members,t),t.scheduleRender()}remove(t){if(_(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,n=this.members.findIndex(e=>t===e);if(0===n)return!1;for(let t=n;t>=0;t--){let n=this.members[t];if(!1!==n.isPresent){e=n;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iz={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},i$=["","X","Y","Z"],iI={visibility:"hidden"},iU=0;function iq(t,e,n,i){let{latestValues:r}=e;r[t]&&(n[t]=r[t],e.setStaticValue(t,0),i&&(i[t]=0))}function iX({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:r}){return class{constructor(t={},n=e?.()){this.id=iU++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,f.value&&(iz.nodes=iz.calculatedTargetDeltas=iz.calculatedProjections=0),this.nodes.forEach(iH),this.nodes.forEach(i1),this.nodes.forEach(i2),this.nodes.forEach(iK),f.addProjectionMetrics&&f.addProjectionMetrics(iz)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ip)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new M),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=ic(e)&&!(ic(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:n,layout:i,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||n)&&(this.isLayoutDirty=!0),t){let n,i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){let n=T.now(),i=({timestamp:r})=>{let s=r-n;s>=250&&(m(i),t(s-e))};return p.setup(i,!0),()=>m(i)}(i,250),ii.hasAnimatedSinceResize&&(ii.hasAnimatedSinceResize=!1,this.nodes.forEach(i0))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&r&&(n||i)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||i6,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),u=!this.targetLayout||!iL(this.targetLayout,i),h=!e&&n;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||i0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(i5),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:n}=e.options;if(!n)return;let i=n.props[R];if(window.MotionHasOptimisedAnimation(i,"transform")){let{layout:t,layoutId:n}=e.options;window.MotionCancelOptimisedAnimation(i,"transform",p,!(t||n))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iZ);return}this.isUpdating||this.nodes.forEach(iQ),this.isUpdating=!1,this.nodes.forEach(iJ),this.nodes.forEach(iW),this.nodes.forEach(iY),this.clearAllSnapshots();let t=T.now();v.delta=B(0,1e3/60,t-v.timestamp),v.timestamp=t,v.isProcessing=!0,y.update.process(v),y.preRender.process(v),y.render.process(v),v.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n9.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iG),this.sharedNodes.forEach(i3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nw(this.snapshot.measuredBox.x)||nw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nE(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!iN(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,s=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||nN(this.latestValues)||s)&&(r(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let n=this.measurePageBox(),i=this.removeElementScroll(n);return t&&(i=this.removeTransform(i)),re((e=i).x),re(e.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return nE();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ri))){let{scroll:t}=this.root;t&&(nF(e.x,t.offset.x),nF(e.y,t.offset.y))}return e}removeElementScroll(t){let e=nE();if(iT(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){let i=this.path[n],{scroll:r,options:s}=i;i!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&iT(e,t),nF(e.x,r.offset.x),nF(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let n=nE();iT(n,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nz(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),nN(i.latestValues)&&nz(n,i.latestValues)}return nN(this.latestValues)&&nz(n,this.latestValues),n}removeTransform(t){let e=nE();iT(e,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];if(!n.instance||!nN(n.latestValues))continue;nD(n.latestValues)&&n.updateSnapshot();let i=nE();iT(i,n.measurePageBox()),iC(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return nN(this.latestValues)&&iC(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==v.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:i,layoutId:r}=this.options;if(this.layout&&(i||r)){if(this.resolvedRelativeTargetAt=v.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nE(),this.relativeTargetOrigin=nE(),nT(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nE(),this.targetWithTransforms=nE()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,nM(s.x,o.x,a.x),nM(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iT(this.target,this.layout.layoutBox),nB(this.target,this.targetDelta)):iT(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nE(),this.relativeTargetOrigin=nE(),nT(this.relativeTargetOrigin,this.target,t.target),iT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}f.value&&iz.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nD(this.parent.latestValues)||nR(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===v.timestamp&&(n=!1),n)return;let{layout:i,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||r))return;iT(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,n,i=!1){let r,s,o=n.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=n[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(i&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nz(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,nB(t,s)),i&&nN(r.latestValues)&&nz(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=nE());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(iS(this.prevProjectionDelta.x,this.projectionDelta.x),iS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),n_(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&iF(this.projectionDelta.x,this.prevProjectionDelta.x)&&iF(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),f.value&&iz.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nP(),this.projectionDelta=nP(),this.projectionDeltaWithTransform=nP()}setAnimationOrigin(t,e=!1){let n,i=this.snapshot,r=i?i.latestValues:{},s={...this.latestValues},o=nP();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=nE(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(i9));this.animationProgress=0,this.mixTargetDelta=e=>{let i=e/1e3;if(i4(o.x,t.x,i),i4(o.y,t.y,i),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,f,d,p,m,v;nT(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),d=this.relativeTarget,p=this.relativeTargetOrigin,m=a,v=i,i8(d.x,p.x,m.x,v),i8(d.y,p.y,m.y,v),n&&(u=this.relativeTarget,f=n,iR(u.x,f.x)&&iR(u.y,f.y))&&(this.isProjectionDirty=!1),n||(n=nE()),iT(n,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,n,i,r,s){r?(t.opacity=tS(0,n.opacity??1,ib(i)),t.opacityExit=tS(e.opacity??1,0,i_(i))):s&&(t.opacity=tS(e.opacity??1,n.opacity??1,i));for(let r=0;r<iy;r++){let s=`border${iv[r]}Radius`,o=iw(e,s),a=iw(n,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||ix(o)===ix(a)?(t[s]=Math.max(tS(ig(o),ig(a),i),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=tS(e.rotate||0,n.rotate||0,i))}(s,r,this.latestValues,i,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{ii.hasAnimatedSinceResize=!0,z.layout++,this.motionValue||(this.motionValue=E(0)),this.currentAnimation=function(t,e,n){let i=C(t)?t:E(t);return i.start(e8("",i,e,n)),i.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{z.layout--},onComplete:()=>{z.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:n,layout:i,latestValues:r}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&rn(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||nE();let e=nw(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;let i=nw(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}iT(e,n),nz(e,r),n_(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iO),this.sharedNodes.get(t).add(e);let n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){let i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;let i={};n.z&&iq("z",t,i,this.animationValues);for(let e=0;e<i$.length;e++)iq(`rotate${i$[e]}`,t,i,this.animationValues),iq(`skew${i$[e]}`,t,i,this.animationValues);for(let e in t.render(),i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return iI;let e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=im(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;let i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=im(t?.pointerEvents)||""),this.hasProjected&&!nN(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let r=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=n?.z||0;if((r||s||o)&&(i=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),n){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),r&&(i+=`rotateX(${r}deg) `),s&&(i+=`rotateY(${s}deg) `),o&&(i+=`skewX(${o}deg) `),a&&(i+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),n&&(e.transform=n(r,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,i.animationValues?e.opacity=i===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=i===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,io){if(void 0===r[t])continue;let{correct:n,applyTo:s,isCSSVariable:o}=io[t],a="none"===e.transform?r[t]:n(r[t],i);if(s){let t=s.length;for(let n=0;n<t;n++)e[s[n]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=i===this?im(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(iZ),this.root.sharedNodes.clear()}}}function iW(t){t.updateLayout()}function iY(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:i}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?nV(t=>{let i=s?e.measuredBox[t]:e.layoutBox[t],r=nw(i);i.min=n[t].min,i.max=i.min+r}):rn(r,e.layoutBox,n)&&nV(i=>{let r=s?e.measuredBox[i]:e.layoutBox[i],o=nw(n[i]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+o)});let o=nP();n_(o,n,e.layoutBox);let a=nP();s?n_(a,t.applyTransform(i,!0),e.measuredBox):n_(a,n,e.layoutBox);let l=!iN(o),u=!1;if(!t.resumeFrom){let i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:r,layout:s}=i;if(r&&s){let o=nE();nT(o,e.layoutBox,r.layoutBox);let a=nE();nT(a,n,s.layoutBox),iL(o,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function iH(t){f.value&&iz.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iK(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iG(t){t.clearSnapshot()}function iZ(t){t.clearMeasurements()}function iQ(t){t.isLayoutDirty=!1}function iJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function i0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function i1(t){t.resolveTargetDelta()}function i2(t){t.calcProjection()}function i5(t){t.resetSkewAndRotation()}function i3(t){t.removeLeadSnapshot()}function i4(t,e,n){t.translate=tS(e.translate,0,n),t.scale=tS(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function i8(t,e,n,i){t.min=tS(e.min,n.min,i),t.max=tS(e.max,n.max,i)}function i9(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let i6={duration:.45,ease:[.4,0,.1,1]},i7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=i7("applewebkit/")&&!i7("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function rn(t,e,n){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(iB(e)-iB(n)))}function ri(t){return t!==t.root&&t.scroll?.wasRoot}let rr=iX({attachResizeListener:(t,e)=>np(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ro=iX({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ra(t,e){let n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,n=(void 0)??e.querySelectorAll(t);return n?Array.from(n):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function rl(t){return!("touch"===t.pointerType||nd.x||nd.y)}function ru(t,e,n){let{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);let r=i["onHover"+n];r&&p.postRender(()=>r(e,nv(e)))}class rh extends nu{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){let[i,r,s]=ra(t,n),o=t=>{if(!rl(t))return;let{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;let s=t=>{rl(t)&&(i(t),n.removeEventListener("pointerleave",s))};n.addEventListener("pointerleave",s,r)};return i.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rc extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=L(np(this.node.current,"focus",()=>this.onFocus()),np(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rf=(t,e)=>!!e&&(t===e||rf(t,e.parentElement)),rd=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rp=new WeakSet;function rm(t){return e=>{"Enter"===e.key&&t(e)}}function rv(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ry=(t,e)=>{let n=t.currentTarget;if(!n)return;let i=rm(()=>{if(rp.has(n))return;rv(n,"down");let t=rm(()=>{rv(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>rv(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)};function rg(t){return nm(t)&&!(nd.x||nd.y)}function rx(t,e,n){let{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);let r=i["onTap"+("End"===n?"":n)];r&&p.postRender(()=>r(e,nv(e)))}class rw extends nu{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){let[i,r,s]=ra(t,n),o=t=>{let i=t.currentTarget;if(!rg(t))return;rp.add(i);let s=e(i,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rp.has(i)&&rp.delete(i),rg(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,i===window||i===document||n.useGlobalTarget||rf(i,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return i.forEach(t=>{((n.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,eG.s)(t))&&(t.addEventListener("focus",t=>ry(t,r)),rd.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rx(this.node,e,"Start"),(t,{success:e})=>rx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rb=new WeakMap,r_=new WeakMap,rM=t=>{let e=rb.get(t.target);e&&e(t)},rA=t=>{t.forEach(rM)},rT={some:0,all:1};class rS extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:rT[i]};return function(t,e,n){let i=function({root:t,...e}){let n=t||document;r_.has(n)||r_.set(n,{});let i=r_.get(n),r=JSON.stringify(e);return i[r]||(i[r]=new IntersectionObserver(rA,{root:t,...e})),i[r]}(e);return rb.set(t,n),i.observe(t),()=>{rb.delete(t),i.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),s=e?n:i;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}let rP=(0,n6.createContext)({strict:!1});var rk=n(1508);let rE=(0,n6.createContext)({});function rV(t){return r(t.animate)||ni.some(e=>ne(t[e]))}function rC(t){return!!(rV(t)||t.variants)}function rD(t){return Array.isArray(t)?t.join(" "):t}var rN=n(8972);let rR={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rj={};for(let t in rR)rj[t]={isEnabled:e=>rR[t].some(t=>!!e[t])};let rL=Symbol.for("motionComponentSymbol");var rB=n(845),rF=n(7494);function rO(t,{layout:e,layoutId:n}){return x.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!io[t]||"opacity"===t)}let rz=(t,e)=>e&&"number"==typeof t?e.transform(t):t,r$={...H,transform:Math.round},rI={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:K,originX:tf,originY:tf,originZ:tu,zIndex:r$,fillOpacity:K,strokeOpacity:K,numOctaves:r$},rU={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rq=g.length;function rX(t,e,n){let{style:i,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let n=e[t];if(x.has(t)){o=!0;continue}if(q(t)){r[t]=n;continue}{let e=rz(n,rI[t]);t.startsWith("origin")?(a=!0,s[t]=e):i[t]=e}}if(!e.transform&&(o||n?i.transform=function(t,e,n){let i="",r=!0;for(let s=0;s<rq;s++){let o=g[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||n){let t=rz(a,rI[o]);if(!l){r=!1;let e=rU[o]||o;i+=`${e}(${t}) `}n&&(e[o]=t)}}return i=i.trim(),n?i=n(e,r?"":i):r&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:n=0}=s;i.transformOrigin=`${t} ${e} ${n}`}}let rW=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rY(t,e,n){for(let i in e)C(e[i])||rO(i,n)||(t[i]=e[i])}let rH={offset:"stroke-dashoffset",array:"stroke-dasharray"},rK={offset:"strokeDashoffset",array:"strokeDasharray"};function rG(t,{attrX:e,attrY:n,attrScale:i,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(rX(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:f}=t;c.transform&&(f.transform=c.transform,delete c.transform),(f.transform||c.transformOrigin)&&(f.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),f.transform&&(f.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==i&&(c.scale=i),void 0!==r&&function(t,e,n=1,i=0,r=!0){t.pathLength=1;let s=r?rH:rK;t[s.offset]=tu.transform(-i);let o=tu.transform(e),a=tu.transform(n);t[s.array]=`${o} ${a}`}(c,r,s,o,!1)}let rZ=()=>({...rW(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r1=t=>!r0(t);try{!function(t){"function"==typeof t&&(r1=e=>e.startsWith("on")?!r0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r5(t){if("string"!=typeof t||t.includes("-"));else if(r2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r3=n(2885);let r4=t=>(e,n)=>{let i=(0,n6.useContext)(rE),s=(0,n6.useContext)(rB.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:function(t,e,n,i){let s={},a=i(t,{});for(let t in a)s[t]=im(a[t]);let{initial:l,animate:u}=t,h=rV(t),c=rC(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let f=!!n&&!1===n.initial,d=(f=f||!1===l)?u:l;if(d&&"boolean"!=typeof d&&!r(d)){let e=Array.isArray(d)?d:[d];for(let n=0;n<e.length;n++){let i=o(t,e[n]);if(i){let{transitionEnd:t,transition:e,...n}=i;for(let t in n){let e=n[t];if(Array.isArray(e)){let t=f?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(n,i,s,t),renderState:e()}})(t,e,i,s);return n?a():(0,r3.M)(a)};function r8(t,e,n){let{style:i}=t,r={};for(let s in i)(C(i[s])||e.style&&C(e.style[s])||rO(s,t)||n?.getValue(s)?.liveStyle!==void 0)&&(r[s]=i[s]);return r}let r9={useVisualState:r4({scrapeMotionValuesFromProps:r8,createRenderState:rW})};function r6(t,e,n){let i=r8(t,e,n);for(let n in t)(C(t[n])||C(e[n]))&&(i[-1!==g.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return i}let r7={useVisualState:r4({scrapeMotionValuesFromProps:r6,createRenderState:rZ})},st=t=>e=>e.test(t),se=[H,tu,tl,ta,tc,th,{test:t=>"auto"===t,parse:t=>t}],sn=t=>se.find(st(t)),si=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[i]=n.match(Q)||[];if(!i)return t;let r=n.replace(i,""),s=+!!so.has(e);return i!==n&&(s*=100),e+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tM,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(sa).join(" "):t}},sh={...rI,color:tp,backgroundColor:tp,outlineColor:tp,fill:tp,stroke:tp,borderColor:tp,borderTopColor:tp,borderRightColor:tp,borderBottomColor:tp,borderLeftColor:tp,filter:su,WebkitFilter:su},sc=t=>sh[t];function sf(t,e){let n=sc(t);return n!==su&&(n=tM),n.getAnimatableNone?n.getAnimatableNone(e):void 0}let sd=new Set(["auto","none","0"]);class sp extends ej{constructor(t,e,n,i,r){super(t,e,n,i,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&W(i=i.trim())){let r=function t(e,n,i=1){I(i<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=sr.exec(t);if(!e)return[,];let[,n,i,r]=e;return[`--${n??i}`,r]}(e);if(!r)return;let o=window.getComputedStyle(n).getPropertyValue(r);if(o){let t=o.trim();return si(t)?parseFloat(t):t}return W(s)?t(s,n,i+1):s}(i,e.current);void 0!==r&&(t[n]=r),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!w.has(n)||2!==t.length)return;let[i,r]=t,s=sn(i),o=sn(r);if(s!==o)if(eT(s)&&eT(o))for(let e=0;e<t.length;e++){let n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else ek[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++){var i;(null===t[e]||("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||ss(i)))&&n.push(e)}n.length&&function(t,e,n){let i,r=0;for(;r<t.length&&!i;){let e=t[r];"string"==typeof e&&!sd.has(e)&&tx(e).values.length&&(i=t[r]),r++}if(i&&n)for(let r of e)t[r]=sf(n,i)}(t,n,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ek[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;let i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);let r=n.length-1,s=n[r];n[r]=ek[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}let sm=[...se,tp,tM],sv=t=>sm.find(st(t)),sy={current:null},sg={current:!1},sx=new WeakMap,sw=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sb{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ej,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=T.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,p.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rV(e),this.isVariantNode=rC(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&C(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sx.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sg.current||function(){if(sg.current=!0,rN.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sy.current=t.matches;t.addListener(e),e()}else sy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let n;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let i=x.has(t);i&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&p.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),n&&n(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rj){let e=rj[t];if(!e)continue;let{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nE()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sw.length;e++){let n=sw[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(let i in e){let r=e[i],s=n[i];if(C(r))t.addValue(i,r);else if(C(s))t.addValue(i,E(r,{owner:t}));else if(s!==r)if(t.hasValue(i)){let e=t.getValue(i);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(i);t.addValue(i,E(void 0!==e?e:r,{owner:t}))}}for(let i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=E(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(si(n)||ss(n))?n=parseFloat(n):!sv(n)&&tM.test(e)&&(n=sf(t,e)),this.setBaseTarget(t,C(n)?n.get():n)),C(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let i=o(this.props,n,this.presenceContext?.custom);i&&(e=i[t])}if(n&&void 0!==e)return e;let i=this.getBaseTargetFromProps(this.props,t);return void 0===i||C(i)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new M),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class s_ extends sb{constructor(){super(...arguments),this.KeyframeResolver=sp}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;C(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sM(t,{style:e,vars:n},i,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(i)),n)t.style.setProperty(s,n[s])}class sA extends s_{constructor(){super(...arguments),this.type="html",this.renderInstance=sM}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?eb(e):eM(t,e);{let n=window.getComputedStyle(t),i=(q(e)?n.getPropertyValue(e):n[e])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:e}){return n$(t,e)}build(t,e,n){rX(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return r8(t,e,n)}}let sT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sS extends s_{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nE}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=sc(e);return t&&t.default||0}return e=sT.has(e)?e:N(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return r6(t,e,n)}build(t,e,n){rG(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){for(let n in sM(t,e,void 0,i),e.attrs)t.setAttribute(sT.has(n)?n:N(n),e.attrs[n])}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let sP=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}((eH={animation:{Feature:nh},exit:{Feature:nf},inView:{Feature:rS},tap:{Feature:rw},focus:{Feature:rc},hover:{Feature:rh},pan:{Feature:n4},drag:{Feature:n5,ProjectionNode:ro,MeasureLayout:il},layout:{ProjectionNode:ro,MeasureLayout:il}},eK=(t,e)=>r5(t)?new sS(e):new sA(e,{allowProjection:t!==n6.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,n;let{preloadedFeatures:i,createVisualElement:r,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var n,i,l;let u,h={...(0,n6.useContext)(rk.Q),...t,layoutId:function(t){let{layoutId:e}=t,n=(0,n6.useContext)(it.L).id;return n&&void 0!==e?n+"-"+e:e}(t)},{isStatic:c}=h,f=function(t){let{initial:e,animate:n}=function(t,e){if(rV(t)){let{initial:e,animate:n}=t;return{initial:!1===e||ne(e)?e:void 0,animate:ne(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,(0,n6.useContext)(rE));return(0,n6.useMemo)(()=>({initial:e,animate:n}),[rD(e),rD(n)])}(t),d=o(t,c);if(!c&&rN.B){i=0,l=0,(0,n6.useContext)(rP).strict;let t=function(t){let{drag:e,layout:n}=rj;if(!e&&!n)return{};let i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(h);u=t.MeasureLayout,f.visualElement=function(t,e,n,i,r){let{visualElement:s}=(0,n6.useContext)(rE),o=(0,n6.useContext)(rP),a=(0,n6.useContext)(rB.t),l=(0,n6.useContext)(rk.Q).reducedMotion,u=(0,n6.useRef)(null);i=i||o.renderer,!u.current&&i&&(u.current=i(t,{visualState:e,parent:s,props:n,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,c=(0,n6.useContext)(ie);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,n,i){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&nU(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:i,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,n,r,c);let f=(0,n6.useRef)(!1);(0,n6.useInsertionEffect)(()=>{h&&f.current&&h.update(n,a)});let d=n[R],p=(0,n6.useRef)(!!d&&!window.MotionHandoffIsComplete?.(d)&&window.MotionHasOptimisedAnimation?.(d));return(0,rF.E)(()=>{h&&(f.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),n9.render(h.render),p.current&&h.animationState&&h.animationState.animateChanges())}),(0,n6.useEffect)(()=>{h&&(!p.current&&h.animationState&&h.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(d)}),p.current=!1))}),h}(a,d,h,r,t.ProjectionNode)}return(0,n8.jsxs)(rE.Provider,{value:f,children:[u&&f.visualElement?(0,n8.jsx)(u,{visualElement:f.visualElement,...h}):null,s(a,t,(n=f.visualElement,(0,n6.useCallback)(t=>{t&&d.onMount&&d.onMount(t),n&&(t?n.mount(t):n.unmount()),e&&("function"==typeof e?e(t):nU(e)&&(e.current=t))},[n])),d,c,f.visualElement)]})}i&&function(t){for(let e in t)rj[e]={...rj[e],...t[e]}}(i),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(n=null!=(e=a.displayName)?e:a.name)?n:"",")"));let u=(0,n6.forwardRef)(l);return u[rL]=a,u}({...r5(t)?r7:r9,preloadedFeatures:eH,useRender:function(t=!1){return(e,n,i,{latestValues:r},s)=>{let o=(r5(e)?function(t,e,n,i){let r=(0,n6.useMemo)(()=>{let n=rZ();return rG(n,e,rQ(i),t.transformTemplate,t.style),{...n.attrs,style:{...n.style}}},[e]);if(t.style){let e={};rY(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let n={},i=function(t,e){let n=t.style||{},i={};return rY(i,n,t),Object.assign(i,function({transformTemplate:t},e){return(0,n6.useMemo)(()=>{let n=rW();return rX(n,e,t),Object.assign({},n.vars,n.style)},[e])}(t,e)),i}(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n})(n,r,s,e),a=function(t,e,n){let i={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r1(r)||!0===n&&r0(r)||!e&&!r0(r)||t.draggable&&r.startsWith("onDrag"))&&(i[r]=t[r]);return i}(n,"string"==typeof e,t),l=e!==n6.Fragment?{...a,...o,ref:i}:{},{children:u}=n,h=(0,n6.useMemo)(()=>C(u)?u.get():u,[u]);return(0,n6.createElement)(e,{...l,children:h})}}(e),createVisualElement:eK,Component:t})}))},6898:(t,e,n)=>{function i(t,e){let n;if(void 0===e)for(let e of t)null!=e&&(n<e||void 0===n&&e>=e)&&(n=e);else{let i=-1;for(let r of t)null!=(r=e(r,++i,t))&&(n<r||void 0===n&&r>=r)&&(n=r)}return n}function r(t,e){let n;if(void 0===e)for(let e of t)null!=e&&(n>e||void 0===n&&e>=e)&&(n=e);else{let i=-1;for(let r of t)null!=(r=e(r,++i,t))&&(n>r||void 0===n&&r>=r)&&(n=r)}return n}function s(){}function o(t){return null==t?s:function(){return this.querySelector(t)}}function a(){return[]}function l(t){return null==t?a:function(){return this.querySelectorAll(t)}}function u(t){return function(){return this.matches(t)}}function h(t){return function(e){return e.matches(t)}}n.d(e,{yWT:()=>tG,jTM:()=>eF,eRw:()=>eZ,xJS:()=>e2,tXi:()=>e1,KS8:()=>e5,TSS:()=>e3,T9B:()=>i,jkA:()=>r,m4Y:()=>function t(){var e=nm()(nh,nh);return e.copy=function(){return np(e,t())},nv.apply(e,arguments),nP(e)},Bv9:()=>nC,Ltv:()=>nD,s_O:()=>nG,GSI:()=>n$});var c,f,d,p,m,v=Array.prototype.find;function y(){return this.firstElementChild}var g=Array.prototype.filter;function x(){return Array.from(this.children)}function w(t){return Array(t.length)}function b(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function _(t,e,n,i,r,s){for(var o,a=0,l=e.length,u=s.length;a<u;++a)(o=e[a])?(o.__data__=s[a],i[a]=o):n[a]=new b(t,s[a]);for(;a<l;++a)(o=e[a])&&(r[a]=o)}function M(t,e,n,i,r,s,o){var a,l,u,h=new Map,c=e.length,f=s.length,d=Array(c);for(a=0;a<c;++a)(l=e[a])&&(d[a]=u=o.call(l,l.__data__,a,e)+"",h.has(u)?r[a]=l:h.set(u,l));for(a=0;a<f;++a)u=o.call(t,s[a],a,s)+"",(l=h.get(u))?(i[a]=l,l.__data__=s[a],h.delete(u)):n[a]=new b(t,s[a]);for(a=0;a<c;++a)(l=e[a])&&h.get(d[a])===l&&(r[a]=l)}function A(t){return t.__data__}function T(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}b.prototype={constructor:b,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var S="http://www.w3.org/1999/xhtml";let P={svg:"http://www.w3.org/2000/svg",xhtml:S,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function k(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),P.hasOwnProperty(e)?{space:P[e],local:t}:t}function E(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function V(t,e){return t.style.getPropertyValue(e)||E(t).getComputedStyle(t,null).getPropertyValue(e)}function C(t){return t.trim().split(/^|\s+/)}function D(t){return t.classList||new N(t)}function N(t){this._node=t,this._names=C(t.getAttribute("class")||"")}function R(t,e){for(var n=D(t),i=-1,r=e.length;++i<r;)n.add(e[i])}function j(t,e){for(var n=D(t),i=-1,r=e.length;++i<r;)n.remove(e[i])}function L(){this.textContent=""}function B(){this.innerHTML=""}function F(){this.nextSibling&&this.parentNode.appendChild(this)}function O(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function z(t){var e=k(t);return(e.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===S&&e.documentElement.namespaceURI===S?e.createElement(t):e.createElementNS(n,t)}})(e)}function $(){return null}function I(){var t=this.parentNode;t&&t.removeChild(this)}function U(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function q(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function X(t){return function(){var e=this.__on;if(e){for(var n,i=0,r=-1,s=e.length;i<s;++i)(n=e[i],t.type&&n.type!==t.type||n.name!==t.name)?e[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?e.length=r:delete this.__on}}}function W(t,e,n){return function(){var i,r=this.__on,s=function(t){e.call(this,t,this.__data__)};if(r){for(var o=0,a=r.length;o<a;++o)if((i=r[o]).type===t.type&&i.name===t.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=s,i.options=n),i.value=e;return}}this.addEventListener(t.type,s,n),i={type:t.type,name:t.name,value:e,listener:s,options:n},r?r.push(i):this.__on=[i]}}function Y(t,e,n){var i=E(t),r=i.CustomEvent;"function"==typeof r?r=new r(e,n):(r=i.document.createEvent("Event"),n?(r.initEvent(e,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(e,!1,!1)),t.dispatchEvent(r)}N.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var H=[null];function K(t,e){this._groups=t,this._parents=e}function G(){return new K([[document.documentElement]],H)}K.prototype=G.prototype={constructor:K,select:function(t){"function"!=typeof t&&(t=o(t));for(var e=this._groups,n=e.length,i=Array(n),r=0;r<n;++r)for(var s,a,l=e[r],u=l.length,h=i[r]=Array(u),c=0;c<u;++c)(s=l[c])&&(a=t.call(s,s.__data__,c,l))&&("__data__"in s&&(a.__data__=s.__data__),h[c]=a);return new K(i,this._parents)},selectAll:function(t){if("function"==typeof t){var e;e=t,t=function(){var t;return t=e.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=l(t);for(var n=this._groups,i=n.length,r=[],s=[],o=0;o<i;++o)for(var a,u=n[o],h=u.length,c=0;c<h;++c)(a=u[c])&&(r.push(t.call(a,a.__data__,c,u)),s.push(a));return new K(r,s)},selectChild:function(t){var e;return this.select(null==t?y:(e="function"==typeof t?t:h(t),function(){return v.call(this.children,e)}))},selectChildren:function(t){var e;return this.selectAll(null==t?x:(e="function"==typeof t?t:h(t),function(){return g.call(this.children,e)}))},filter:function(t){"function"!=typeof t&&(t=u(t));for(var e=this._groups,n=e.length,i=Array(n),r=0;r<n;++r)for(var s,o=e[r],a=o.length,l=i[r]=[],h=0;h<a;++h)(s=o[h])&&t.call(s,s.__data__,h,o)&&l.push(s);return new K(i,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,A);var n=e?M:_,i=this._parents,r=this._groups;"function"!=typeof t&&(g=t,t=function(){return g});for(var s=r.length,o=Array(s),a=Array(s),l=Array(s),u=0;u<s;++u){var h=i[u],c=r[u],f=c.length,d="object"==typeof(y=t.call(h,h&&h.__data__,u,i))&&"length"in y?y:Array.from(y),p=d.length,m=a[u]=Array(p),v=o[u]=Array(p);n(h,c,m,v,l[u]=Array(f),d,e);for(var y,g,x,w,b=0,T=0;b<p;++b)if(x=m[b]){for(b>=T&&(T=b+1);!(w=v[T])&&++T<p;);x._next=w||null}}return(o=new K(o,i))._enter=a,o._exit=l,o},enter:function(){return new K(this._enter||this._groups.map(w),this._parents)},exit:function(){return new K(this._exit||this._groups.map(w),this._parents)},join:function(t,e,n){var i=this.enter(),r=this,s=this.exit();return"function"==typeof t?(i=t(i))&&(i=i.selection()):i=i.append(t+""),null!=e&&(r=e(r))&&(r=r.selection()),null==n?s.remove():n(s),i&&r?i.merge(r).order():r},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,i=e._groups,r=n.length,s=i.length,o=Math.min(r,s),a=Array(r),l=0;l<o;++l)for(var u,h=n[l],c=i[l],f=h.length,d=a[l]=Array(f),p=0;p<f;++p)(u=h[p]||c[p])&&(d[p]=u);for(;l<r;++l)a[l]=n[l];return new K(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var i,r=t[e],s=r.length-1,o=r[s];--s>=0;)(i=r[s])&&(o&&4^i.compareDocumentPosition(o)&&o.parentNode.insertBefore(i,o),o=i);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=T);for(var n=this._groups,i=n.length,r=Array(i),s=0;s<i;++s){for(var o,a=n[s],l=a.length,u=r[s]=Array(l),h=0;h<l;++h)(o=a[h])&&(u[h]=o);u.sort(e)}return new K(r,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var i=t[e],r=0,s=i.length;r<s;++r){var o=i[r];if(o)return o}return null},size:function(){let t=0;for(let e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,i=e.length;n<i;++n)for(var r,s=e[n],o=0,a=s.length;o<a;++o)(r=s[o])&&t.call(r,r.__data__,o,s);return this},attr:function(t,e){var n=k(t);if(arguments.length<2){var i=this.node();return n.local?i.getAttributeNS(n.space,n.local):i.getAttribute(n)}return this.each((null==e?n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof e?n.local?function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}:function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}:n.local?function(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}:function(t,e){return function(){this.setAttribute(t,e)}})(n,e))},style:function(t,e,n){return arguments.length>1?this.each((null==e?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof e?function(t,e,n){return function(){var i=e.apply(this,arguments);null==i?this.style.removeProperty(t):this.style.setProperty(t,i,n)}}:function(t,e,n){return function(){this.style.setProperty(t,e,n)}})(t,e,null==n?"":n)):V(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?function(t){return function(){delete this[t]}}:"function"==typeof e?function(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}:function(t,e){return function(){this[t]=e}})(t,e)):this.node()[t]},classed:function(t,e){var n=C(t+"");if(arguments.length<2){for(var i=D(this.node()),r=-1,s=n.length;++r<s;)if(!i.contains(n[r]))return!1;return!0}return this.each(("function"==typeof e?function(t,e){return function(){(e.apply(this,arguments)?R:j)(this,t)}}:e?function(t){return function(){R(this,t)}}:function(t){return function(){j(this,t)}})(n,e))},text:function(t){return arguments.length?this.each(null==t?L:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?B:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(F)},lower:function(){return this.each(O)},append:function(t){var e="function"==typeof t?t:z(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})},insert:function(t,e){var n="function"==typeof t?t:z(t),i=null==e?$:"function"==typeof e?e:o(e);return this.select(function(){return this.insertBefore(n.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each(I)},clone:function(t){return this.select(t?q:U)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var i,r,s=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}),o=s.length;if(arguments.length<2){var a=this.node().__on;if(a){for(var l,u=0,h=a.length;u<h;++u)for(i=0,l=a[u];i<o;++i)if((r=s[i]).type===l.type&&r.name===l.name)return l.value}return}for(i=0,a=e?W:X;i<o;++i)this.each(a(s[i],e,n));return this},dispatch:function(t,e){return this.each(("function"==typeof e?function(t,e){return function(){return Y(this,t,e.apply(this,arguments))}}:function(t,e){return function(){return Y(this,t,e)}})(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var i,r=t[e],s=0,o=r.length;s<o;++s)(i=r[s])&&(yield i)}};var Z={value:()=>{}};function Q(){for(var t,e=0,n=arguments.length,i={};e<n;++e){if(!(t=arguments[e]+"")||t in i||/[\s.]/.test(t))throw Error("illegal type: "+t);i[t]=[]}return new J(i)}function J(t){this._=t}function tt(t,e,n){for(var i=0,r=t.length;i<r;++i)if(t[i].name===e){t[i]=Z,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=n&&t.push({name:e,value:n}),t}J.prototype=Q.prototype={constructor:J,on:function(t,e){var n,i=this._,r=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!i.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:e}}),s=-1,o=r.length;if(arguments.length<2){for(;++s<o;)if((n=(t=r[s]).type)&&(n=function(t,e){for(var n,i=0,r=t.length;i<r;++i)if((n=t[i]).name===e)return n.value}(i[n],t.name)))return n;return}if(null!=e&&"function"!=typeof e)throw Error("invalid callback: "+e);for(;++s<o;)if(n=(t=r[s]).type)i[n]=tt(i[n],t.name,e);else if(null==e)for(n in i)i[n]=tt(i[n],t.name,null);return this},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new J(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,i,r=Array(n),s=0;s<n;++s)r[s]=arguments[s+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(i=this._[t],s=0,n=i.length;s<n;++s)i[s].value.apply(e,r)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var i=this._[t],r=0,s=i.length;r<s;++r)i[r].value.apply(e,n)}};var te,tn,ti=0,tr=0,ts=0,to=0,ta=0,tl=0,tu="object"==typeof performance&&performance.now?performance:Date,th="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function tc(){return ta||(th(tf),ta=tu.now()+tl)}function tf(){ta=0}function td(){this._call=this._time=this._next=null}function tp(t,e,n){var i=new td;return i.restart(t,e,n),i}function tm(){ta=(to=tu.now())+tl,ti=tr=0;try{tc(),++ti;for(var t,e=te;e;)(t=ta-e._time)>=0&&e._call.call(void 0,t),e=e._next;--ti}finally{ti=0,function(){for(var t,e,n=te,i=1/0;n;)n._call?(i>n._time&&(i=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:te=e);tn=t,ty(i)}(),ta=0}}function tv(){var t=tu.now(),e=t-to;e>1e3&&(tl-=e,to=t)}function ty(t){!ti&&(tr&&(tr=clearTimeout(tr)),t-ta>24?(t<1/0&&(tr=setTimeout(tm,t-tu.now()-tl)),ts&&(ts=clearInterval(ts))):(ts||(to=tu.now(),ts=setInterval(tv,1e3)),ti=1,th(tm)))}function tg(t,e,n){var i=new td;return e=null==e?0:+e,i.restart(n=>{i.stop(),t(n+e)},e,n),i}td.prototype=tp.prototype={constructor:td,restart:function(t,e,n){if("function"!=typeof t)throw TypeError("callback is not a function");n=(null==n?tc():+n)+(null==e?0:+e),this._next||tn===this||(tn?tn._next=this:te=this,tn=this),this._call=t,this._time=n,ty()},stop:function(){this._call&&(this._call=null,this._time=1/0,ty())}};var tx=Q("start","end","cancel","interrupt"),tw=[];function tb(t,e,n,i,r,s){var o=t.__transition;if(o){if(n in o)return}else t.__transition={};!function(t,e,n){var i,r=t.__transition;function s(l){var u,h,c,f;if(1!==n.state)return a();for(u in r)if((f=r[u]).name===n.name){if(3===f.state)return tg(s);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",t,t.__data__,f.index,f.group),delete r[u]):+u<e&&(f.state=6,f.timer.stop(),f.on.call("cancel",t,t.__data__,f.index,f.group),delete r[u])}if(tg(function(){3===n.state&&(n.state=4,n.timer.restart(o,n.delay,n.time),o(l))}),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(u=0,n.state=3,i=Array(c=n.tween.length),h=-1;u<c;++u)(f=n.tween[u].value.call(t,t.__data__,n.index,n.group))&&(i[++h]=f);i.length=h+1}}function o(e){for(var r=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(a),n.state=5,1),s=-1,o=i.length;++s<o;)i[s].call(t,r);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),a())}function a(){for(var i in n.state=6,n.timer.stop(),delete r[e],r)return;delete t.__transition}r[e]=n,n.timer=tp(function(t){n.state=1,n.timer.restart(s,n.delay,n.time),n.delay<=t&&s(t-n.delay)},0,n.time)}(t,n,{name:e,index:i,group:r,on:tx,tween:tw,time:s.time,delay:s.delay,duration:s.duration,ease:s.ease,timer:null,state:0})}function t_(t,e){var n=tA(t,e);if(n.state>0)throw Error("too late; already scheduled");return n}function tM(t,e){var n=tA(t,e);if(n.state>3)throw Error("too late; already running");return n}function tA(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw Error("transition not found");return n}function tT(t,e){var n,i,r,s=t.__transition,o=!0;if(s){for(r in e=null==e?null:e+"",s){if((n=s[r]).name!==e){o=!1;continue}i=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(i?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete s[r]}o&&delete t.__transition}}function tS(t,e){return t*=1,e*=1,function(n){return t*(1-n)+e*n}}var tP=180/Math.PI,tk={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function tE(t,e,n,i,r,s){var o,a,l;return(o=Math.sqrt(t*t+e*e))&&(t/=o,e/=o),(l=t*n+e*i)&&(n-=t*l,i-=e*l),(a=Math.sqrt(n*n+i*i))&&(n/=a,i/=a,l/=a),t*i<e*n&&(t=-t,e=-e,l=-l,o=-o),{translateX:r,translateY:s,rotate:Math.atan2(e,t)*tP,skewX:Math.atan(l)*tP,scaleX:o,scaleY:a}}function tV(t,e,n,i){function r(t){return t.length?t.pop()+" ":""}return function(s,o){var a,l,u,h,c=[],f=[];return s=t(s),o=t(o),!function(t,i,r,s,o,a){if(t!==r||i!==s){var l=o.push("translate(",null,e,null,n);a.push({i:l-4,x:tS(t,r)},{i:l-2,x:tS(i,s)})}else(r||s)&&o.push("translate("+r+e+s+n)}(s.translateX,s.translateY,o.translateX,o.translateY,c,f),a=s.rotate,l=o.rotate,a!==l?(a-l>180?l+=360:l-a>180&&(a+=360),f.push({i:c.push(r(c)+"rotate(",null,i)-2,x:tS(a,l)})):l&&c.push(r(c)+"rotate("+l+i),u=s.skewX,h=o.skewX,u!==h?f.push({i:c.push(r(c)+"skewX(",null,i)-2,x:tS(u,h)}):h&&c.push(r(c)+"skewX("+h+i),!function(t,e,n,i,s,o){if(t!==n||e!==i){var a=s.push(r(s)+"scale(",null,",",null,")");o.push({i:a-4,x:tS(t,n)},{i:a-2,x:tS(e,i)})}else(1!==n||1!==i)&&s.push(r(s)+"scale("+n+","+i+")")}(s.scaleX,s.scaleY,o.scaleX,o.scaleY,c,f),s=o=null,function(t){for(var e,n=-1,i=f.length;++n<i;)c[(e=f[n]).i]=e.x(t);return c.join("")}}}var tC=tV(function(t){let e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?tk:tE(e.a,e.b,e.c,e.d,e.e,e.f)},"px, ","px)","deg)"),tD=tV(function(t){return null==t?tk:(c||(c=document.createElementNS("http://www.w3.org/2000/svg","g")),c.setAttribute("transform",t),t=c.transform.baseVal.consolidate())?tE((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):tk},", ",")",")");function tN(t,e,n){var i=t._id;return t.each(function(){var t=tM(this,i);(t.value||(t.value={}))[e]=n.apply(this,arguments)}),function(t){return tA(t,i).value[e]}}function tR(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function tj(t,e){var n=Object.create(t.prototype);for(var i in e)n[i]=e[i];return n}function tL(){}var tB="\\s*([+-]?\\d+)\\s*",tF="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",tO="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",tz=/^#([0-9a-f]{3,8})$/,t$=RegExp(`^rgb\\(${tB},${tB},${tB}\\)$`),tI=RegExp(`^rgb\\(${tO},${tO},${tO}\\)$`),tU=RegExp(`^rgba\\(${tB},${tB},${tB},${tF}\\)$`),tq=RegExp(`^rgba\\(${tO},${tO},${tO},${tF}\\)$`),tX=RegExp(`^hsl\\(${tF},${tO},${tO}\\)$`),tW=RegExp(`^hsla\\(${tF},${tO},${tO},${tF}\\)$`),tY={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function tH(){return this.rgb().formatHex()}function tK(){return this.rgb().formatRgb()}function tG(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=tz.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?tZ(e):3===n?new t0(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?tQ(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?tQ(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=t$.exec(t))?new t0(e[1],e[2],e[3],1):(e=tI.exec(t))?new t0(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=tU.exec(t))?tQ(e[1],e[2],e[3],e[4]):(e=tq.exec(t))?tQ(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=tX.exec(t))?t8(e[1],e[2]/100,e[3]/100,1):(e=tW.exec(t))?t8(e[1],e[2]/100,e[3]/100,e[4]):tY.hasOwnProperty(t)?tZ(tY[t]):"transparent"===t?new t0(NaN,NaN,NaN,0):null}function tZ(t){return new t0(t>>16&255,t>>8&255,255&t,1)}function tQ(t,e,n,i){return i<=0&&(t=e=n=NaN),new t0(t,e,n,i)}function tJ(t,e,n,i){var r;return 1==arguments.length?((r=t)instanceof tL||(r=tG(r)),r)?new t0((r=r.rgb()).r,r.g,r.b,r.opacity):new t0:new t0(t,e,n,null==i?1:i)}function t0(t,e,n,i){this.r=+t,this.g=+e,this.b=+n,this.opacity=+i}function t1(){return`#${t4(this.r)}${t4(this.g)}${t4(this.b)}`}function t2(){let t=t5(this.opacity);return`${1===t?"rgb(":"rgba("}${t3(this.r)}, ${t3(this.g)}, ${t3(this.b)}${1===t?")":`, ${t})`}`}function t5(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function t3(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function t4(t){return((t=t3(t))<16?"0":"")+t.toString(16)}function t8(t,e,n,i){return i<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new t6(t,e,n,i)}function t9(t){if(t instanceof t6)return new t6(t.h,t.s,t.l,t.opacity);if(t instanceof tL||(t=tG(t)),!t)return new t6;if(t instanceof t6)return t;var e=(t=t.rgb()).r/255,n=t.g/255,i=t.b/255,r=Math.min(e,n,i),s=Math.max(e,n,i),o=NaN,a=s-r,l=(s+r)/2;return a?(o=e===s?(n-i)/a+(n<i)*6:n===s?(i-e)/a+2:(e-n)/a+4,a/=l<.5?s+r:2-s-r,o*=60):a=l>0&&l<1?0:o,new t6(o,a,l,t.opacity)}function t6(t,e,n,i){this.h=+t,this.s=+e,this.l=+n,this.opacity=+i}function t7(t){return(t=(t||0)%360)<0?t+360:t}function et(t){return Math.max(0,Math.min(1,t||0))}function ee(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}function en(t,e,n,i,r){var s=t*t,o=s*t;return((1-3*t+3*s-o)*e+(4-6*s+3*o)*n+(1+3*t+3*s-3*o)*i+o*r)/6}tR(tL,tG,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:tH,formatHex:tH,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return t9(this).formatHsl()},formatRgb:tK,toString:tK}),tR(t0,tJ,tj(tL,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new t0(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new t0(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new t0(t3(this.r),t3(this.g),t3(this.b),t5(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:t1,formatHex:t1,formatHex8:function(){return`#${t4(this.r)}${t4(this.g)}${t4(this.b)}${t4((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:t2,toString:t2})),tR(t6,function(t,e,n,i){return 1==arguments.length?t9(t):new t6(t,e,n,null==i?1:i)},tj(tL,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new t6(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new t6(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,i=n+(n<.5?n:1-n)*e,r=2*n-i;return new t0(ee(t>=240?t-240:t+120,r,i),ee(t,r,i),ee(t<120?t+240:t-120,r,i),this.opacity)},clamp(){return new t6(t7(this.h),et(this.s),et(this.l),t5(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=t5(this.opacity);return`${1===t?"hsl(":"hsla("}${t7(this.h)}, ${100*et(this.s)}%, ${100*et(this.l)}%${1===t?")":`, ${t})`}`}}));let ei=t=>()=>t;function er(t,e){var n,i,r=e-t;return r?(n=t,i=r,function(t){return n+t*i}):ei(isNaN(t)?e:t)}let es=function t(e){var n,i=1==(n=+e)?er:function(t,e){var i,r,s;return e-t?(i=t,r=e,i=Math.pow(i,s=n),r=Math.pow(r,s)-i,s=1/s,function(t){return Math.pow(i+t*r,s)}):ei(isNaN(t)?e:t)};function r(t,e){var n=i((t=tJ(t)).r,(e=tJ(e)).r),r=i(t.g,e.g),s=i(t.b,e.b),o=er(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=r(e),t.b=s(e),t.opacity=o(e),t+""}}return r.gamma=t,r}(1);function eo(t){return function(e){var n,i,r=e.length,s=Array(r),o=Array(r),a=Array(r);for(n=0;n<r;++n)i=tJ(e[n]),s[n]=i.r||0,o[n]=i.g||0,a[n]=i.b||0;return s=t(s),o=t(o),a=t(a),i.opacity=1,function(t){return i.r=s(t),i.g=o(t),i.b=a(t),i+""}}}eo(function(t){var e=t.length-1;return function(n){var i=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),r=t[i],s=t[i+1],o=i>0?t[i-1]:2*r-s,a=i<e-1?t[i+2]:2*s-r;return en((n-i/e)*e,o,r,s,a)}}),eo(function(t){var e=t.length;return function(n){var i=Math.floor(((n%=1)<0?++n:n)*e),r=t[(i+e-1)%e],s=t[i%e],o=t[(i+1)%e],a=t[(i+2)%e];return en((n-i/e)*e,r,s,o,a)}});var ea=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,el=RegExp(ea.source,"g");function eu(t,e){var n,i,r,s,o,a=ea.lastIndex=el.lastIndex=0,l=-1,u=[],h=[];for(t+="",e+="";(r=ea.exec(t))&&(s=el.exec(e));)(o=s.index)>a&&(o=e.slice(a,o),u[l]?u[l]+=o:u[++l]=o),(r=r[0])===(s=s[0])?u[l]?u[l]+=s:u[++l]=s:(u[++l]=null,h.push({i:l,x:tS(r,s)})),a=el.lastIndex;return a<e.length&&(o=e.slice(a),u[l]?u[l]+=o:u[++l]=o),u.length<2?h[0]?(n=h[0].x,function(t){return n(t)+""}):(i=e,function(){return i}):(e=h.length,function(t){for(var n,i=0;i<e;++i)u[(n=h[i]).i]=n.x(t);return u.join("")})}function eh(t,e){var n;return("number"==typeof e?tS:e instanceof tG?es:(n=tG(e))?(e=n,es):eu)(t,e)}var ec=G.prototype.constructor;function ef(t){return function(){this.style.removeProperty(t)}}var ed=0;function ep(t,e,n,i){this._groups=t,this._parents=e,this._name=n,this._id=i}var em=G.prototype;ep.prototype=(function(t){return G().transition(t)}).prototype={constructor:ep,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=o(t));for(var i=this._groups,r=i.length,s=Array(r),a=0;a<r;++a)for(var l,u,h=i[a],c=h.length,f=s[a]=Array(c),d=0;d<c;++d)(l=h[d])&&(u=t.call(l,l.__data__,d,h))&&("__data__"in l&&(u.__data__=l.__data__),f[d]=u,tb(f[d],e,n,d,f,tA(l,n)));return new ep(s,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=l(t));for(var i=this._groups,r=i.length,s=[],o=[],a=0;a<r;++a)for(var u,h=i[a],c=h.length,f=0;f<c;++f)if(u=h[f]){for(var d,p=t.call(u,u.__data__,f,h),m=tA(u,n),v=0,y=p.length;v<y;++v)(d=p[v])&&tb(d,e,n,v,p,m);s.push(p),o.push(u)}return new ep(s,o,e,n)},selectChild:em.selectChild,selectChildren:em.selectChildren,filter:function(t){"function"!=typeof t&&(t=u(t));for(var e=this._groups,n=e.length,i=Array(n),r=0;r<n;++r)for(var s,o=e[r],a=o.length,l=i[r]=[],h=0;h<a;++h)(s=o[h])&&t.call(s,s.__data__,h,o)&&l.push(s);return new ep(i,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var e=this._groups,n=t._groups,i=e.length,r=n.length,s=Math.min(i,r),o=Array(i),a=0;a<s;++a)for(var l,u=e[a],h=n[a],c=u.length,f=o[a]=Array(c),d=0;d<c;++d)(l=u[d]||h[d])&&(f[d]=l);for(;a<i;++a)o[a]=e[a];return new ep(o,this._parents,this._name,this._id)},selection:function(){return new ec(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=++ed,i=this._groups,r=i.length,s=0;s<r;++s)for(var o,a=i[s],l=a.length,u=0;u<l;++u)if(o=a[u]){var h=tA(o,e);tb(o,t,n,u,a,{time:h.time+h.delay+h.duration,delay:0,duration:h.duration,ease:h.ease})}return new ep(i,this._parents,t,n)},call:em.call,nodes:em.nodes,node:em.node,size:em.size,empty:em.empty,each:em.each,on:function(t,e){var n,i,r,s,o,a,l=this._id;return arguments.length<2?tA(this.node(),l).on.on(t):this.each((n=l,i=t,r=e,a=(i+"").trim().split(/^|\s+/).every(function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t})?t_:tM,function(){var t=a(this,n),e=t.on;e!==s&&(o=(s=e).copy()).on(i,r),t.on=o}))},attr:function(t,e){var n=k(t),i="transform"===n?tD:eh;return this.attrTween(t,"function"==typeof e?(n.local?function(t,e,n){var i,r,s;return function(){var o,a,l=n(this);return null==l?void this.removeAttributeNS(t.space,t.local):(o=this.getAttributeNS(t.space,t.local))===(a=l+"")?null:o===i&&a===r?s:(r=a,s=e(i=o,l))}}:function(t,e,n){var i,r,s;return function(){var o,a,l=n(this);return null==l?void this.removeAttribute(t):(o=this.getAttribute(t))===(a=l+"")?null:o===i&&a===r?s:(r=a,s=e(i=o,l))}})(n,i,tN(this,"attr."+t,e)):null==e?(n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(n):(n.local?function(t,e,n){var i,r,s=n+"";return function(){var o=this.getAttributeNS(t.space,t.local);return o===s?null:o===i?r:r=e(i=o,n)}}:function(t,e,n){var i,r,s=n+"";return function(){var o=this.getAttribute(t);return o===s?null:o===i?r:r=e(i=o,n)}})(n,i,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw Error();var i=k(t);return this.tween(n,(i.local?function(t,e){var n,i;function r(){var r=e.apply(this,arguments);return r!==i&&(n=(i=r)&&function(e){this.setAttributeNS(t.space,t.local,r.call(this,e))}),n}return r._value=e,r}:function(t,e){var n,i;function r(){var r=e.apply(this,arguments);return r!==i&&(n=(i=r)&&function(e){this.setAttribute(t,r.call(this,e))}),n}return r._value=e,r})(i,e))},style:function(t,e,n){var i,r,s,o,a,l,u,h,c,f,d,p,m,v,y,g,x,w,b,_,M,A="transform"==(t+="")?tC:eh;return null==e?this.styleTween(t,(i=t,function(){var t=V(this,i),e=(this.style.removeProperty(i),V(this,i));return t===e?null:t===r&&e===s?o:o=A(r=t,s=e)})).on("end.style."+t,ef(t)):"function"==typeof e?this.styleTween(t,(a=t,l=tN(this,"style."+t,e),function(){var t=V(this,a),e=l(this),n=e+"";return null==e&&(this.style.removeProperty(a),n=e=V(this,a)),t===n?null:t===u&&n===h?c:(h=n,c=A(u=t,e))})).each((f=this._id,x="end."+(g="style."+(d=t)),function(){var t=tM(this,f),e=t.on,n=null==t.value[g]?y||(y=ef(d)):void 0;(e!==p||v!==n)&&(m=(p=e).copy()).on(x,v=n),t.on=m})):this.styleTween(t,(w=t,M=e+"",function(){var t=V(this,w);return t===M?null:t===b?_:_=A(b=t,e)}),n).on("end.style."+t,null)},styleTween:function(t,e,n){var i="style."+(t+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(null==e)return this.tween(i,null);if("function"!=typeof e)throw Error();return this.tween(i,function(t,e,n){var i,r;function s(){var s=e.apply(this,arguments);return s!==r&&(i=(r=s)&&function(e){this.style.setProperty(t,s.call(this,e),n)}),i}return s._value=e,s}(t,e,null==n?"":n))},text:function(t){var e,n;return this.tween("text","function"==typeof t?(e=tN(this,"text",t),function(){var t=e(this);this.textContent=null==t?"":t}):(n=null==t?"":t+"",function(){this.textContent=n}))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw Error();return this.tween(e,function(t){var e,n;function i(){var i=t.apply(this,arguments);return i!==n&&(e=(n=i)&&function(t){this.textContent=i.call(this,t)}),e}return i._value=t,i}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var i,r=tA(this.node(),n).tween,s=0,o=r.length;s<o;++s)if((i=r[s]).name===t)return i.value;return null}return this.each((null==e?function(t,e){var n,i;return function(){var r=tM(this,t),s=r.tween;if(s!==n){i=n=s;for(var o=0,a=i.length;o<a;++o)if(i[o].name===e){(i=i.slice()).splice(o,1);break}}r.tween=i}}:function(t,e,n){var i,r;if("function"!=typeof n)throw Error();return function(){var s=tM(this,t),o=s.tween;if(o!==i){r=(i=o).slice();for(var a={name:e,value:n},l=0,u=r.length;l<u;++l)if(r[l].name===e){r[l]=a;break}l===u&&r.push(a)}s.tween=r}})(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){t_(this,t).delay=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){t_(this,t).delay=e}})(e,t)):tA(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){tM(this,t).duration=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){tM(this,t).duration=e}})(e,t)):tA(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw Error();return function(){tM(this,t).ease=e}}(e,t)):tA(this.node(),e).ease},easeVarying:function(t){var e;if("function"!=typeof t)throw Error();return this.each((e=this._id,function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw Error();tM(this,e).ease=n}))},end:function(){var t,e,n=this,i=n._id,r=n.size();return new Promise(function(s,o){var a={value:o},l={value:function(){0==--r&&s()}};n.each(function(){var n=tM(this,i),r=n.on;r!==t&&((e=(t=r).copy())._.cancel.push(a),e._.interrupt.push(a),e._.end.push(l)),n.on=e}),0===r&&s()})},[Symbol.iterator]:em[Symbol.iterator]};var ev={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};G.prototype.interrupt=function(t){return this.each(function(){tT(this,t)})},G.prototype.transition=function(t){var e,n;t instanceof ep?(e=t._id,t=t._name):(e=++ed,(n=ev).time=tc(),t=null==t?null:t+"");for(var i=this._groups,r=i.length,s=0;s<r;++s)for(var o,a=i[s],l=a.length,u=0;u<l;++u)(o=a[u])&&tb(o,t,e,u,a,n||function(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw Error(`transition ${e} not found`);return n}(o,e));return new ep(i,this._parents,t,e)};var ey={name:"drag"},eg={name:"space"},ex={name:"handle"},ew={name:"center"};let{abs:eb,max:e_,min:eM}=Math;function eA(t){return[+t[0],+t[1]]}function eT(t){return[eA(t[0]),eA(t[1])]}var eS={name:"x",handles:["w","e"].map(eN),input:function(t,e){return null==t?null:[[+t[0],e[0][1]],[+t[1],e[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},eP={name:"y",handles:["n","s"].map(eN),input:function(t,e){return null==t?null:[[e[0][0],+t[0]],[e[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},ek=(["n","w","e","s","nw","ne","sw","se"].map(eN),{overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"}),eE={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},eV={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},eC={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},eD={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function eN(t){return{type:t}}function eR(t){return!t.ctrlKey&&!t.button}function ej(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function eL(){return navigator.maxTouchPoints||"ontouchstart"in this}function eB(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function eF(t,e){var n,i=1;function r(){var r,s,o=n.length,a=0,l=0;for(r=0;r<o;++r)a+=(s=n[r]).x,l+=s.y;for(a=(a/o-t)*i,l=(l/o-e)*i,r=0;r<o;++r)s=n[r],s.x-=a,s.y-=l}return null==t&&(t=0),null==e&&(e=0),r.initialize=function(t){n=t},r.x=function(e){return arguments.length?(t=+e,r):t},r.y=function(t){return arguments.length?(e=+t,r):e},r.strength=function(t){return arguments.length?(i=+t,r):i},r}function eO(t,e,n,i){if(isNaN(e)||isNaN(n))return t;var r,s,o,a,l,u,h,c,f,d=t._root,p={data:i},m=t._x0,v=t._y0,y=t._x1,g=t._y1;if(!d)return t._root=p,t;for(;d.length;)if((u=e>=(s=(m+y)/2))?m=s:y=s,(h=n>=(o=(v+g)/2))?v=o:g=o,r=d,!(d=d[c=h<<1|u]))return r[c]=p,t;if(a=+t._x.call(null,d.data),l=+t._y.call(null,d.data),e===a&&n===l)return p.next=d,r?r[c]=p:t._root=p,t;do r=r?r[c]=[,,,,]:t._root=[,,,,],(u=e>=(s=(m+y)/2))?m=s:y=s,(h=n>=(o=(v+g)/2))?v=o:g=o;while((c=h<<1|u)==(f=(l>=o)<<1|a>=s));return r[f]=d,r[c]=p,t}function ez(t,e,n,i,r){this.node=t,this.x0=e,this.y0=n,this.x1=i,this.y1=r}function e$(t){return t[0]}function eI(t){return t[1]}function eU(t,e,n){var i=new eq(null==e?e$:e,null==n?eI:n,NaN,NaN,NaN,NaN);return null==t?i:i.addAll(t)}function eq(t,e,n,i,r,s){this._x=t,this._y=e,this._x0=n,this._y0=i,this._x1=r,this._y1=s,this._root=void 0}function eX(t){for(var e={data:t.data},n=e;t=t.next;)n=n.next={data:t.data};return e}var eW=eU.prototype=eq.prototype;function eY(t){return function(){return t}}function eH(t){return(t()-.5)*1e-6}function eK(t){return t.x+t.vx}function eG(t){return t.y+t.vy}function eZ(t){var e,n,i,r=1,s=1;function o(){for(var t,o,l,u,h,c,f,d=e.length,p=0;p<s;++p)for(t=0,o=eU(e,eK,eG).visitAfter(a);t<d;++t)f=(c=n[(l=e[t]).index])*c,u=l.x+l.vx,h=l.y+l.vy,o.visit(m);function m(t,e,n,s,o){var a=t.data,d=t.r,p=c+d;if(a){if(a.index>l.index){var m=u-a.x-a.vx,v=h-a.y-a.vy,y=m*m+v*v;y<p*p&&(0===m&&(y+=(m=eH(i))*m),0===v&&(y+=(v=eH(i))*v),y=(p-(y=Math.sqrt(y)))/y*r,l.vx+=(m*=y)*(p=(d*=d)/(f+d)),l.vy+=(v*=y)*p,a.vx-=m*(p=1-p),a.vy-=v*p)}return}return e>u+p||s<u-p||n>h+p||o<h-p}}function a(t){if(t.data)return t.r=n[t.data.index];for(var e=t.r=0;e<4;++e)t[e]&&t[e].r>t.r&&(t.r=t[e].r)}function l(){if(e){var i,r,s=e.length;for(i=0,n=Array(s);i<s;++i)n[(r=e[i]).index]=+t(r,i,e)}}return"function"!=typeof t&&(t=eY(null==t?1:+t)),o.initialize=function(t,n){e=t,i=n,l()},o.iterations=function(t){return arguments.length?(s=+t,o):s},o.strength=function(t){return arguments.length?(r=+t,o):r},o.radius=function(e){return arguments.length?(t="function"==typeof e?e:eY(+e),l(),o):t},o}function eQ(t){return t.x}function eJ(t){return t.y}eW.copy=function(){var t,e,n=new eq(this._x,this._y,this._x0,this._y0,this._x1,this._y1),i=this._root;if(!i)return n;if(!i.length)return n._root=eX(i),n;for(t=[{source:i,target:n._root=[,,,,]}];i=t.pop();)for(var r=0;r<4;++r)(e=i.source[r])&&(e.length?t.push({source:e,target:i.target[r]=[,,,,]}):i.target[r]=eX(e));return n},eW.add=function(t){let e=+this._x.call(null,t),n=+this._y.call(null,t);return eO(this.cover(e,n),e,n,t)},eW.addAll=function(t){var e,n,i,r,s=t.length,o=Array(s),a=Array(s),l=1/0,u=1/0,h=-1/0,c=-1/0;for(n=0;n<s;++n)!(isNaN(i=+this._x.call(null,e=t[n]))||isNaN(r=+this._y.call(null,e)))&&(o[n]=i,a[n]=r,i<l&&(l=i),i>h&&(h=i),r<u&&(u=r),r>c&&(c=r));if(l>h||u>c)return this;for(this.cover(l,u).cover(h,c),n=0;n<s;++n)eO(this,o[n],a[n],t[n]);return this},eW.cover=function(t,e){if(isNaN(t*=1)||isNaN(e*=1))return this;var n=this._x0,i=this._y0,r=this._x1,s=this._y1;if(isNaN(n))r=(n=Math.floor(t))+1,s=(i=Math.floor(e))+1;else{for(var o,a,l=r-n||1,u=this._root;n>t||t>=r||i>e||e>=s;)switch(a=(e<i)<<1|t<n,(o=[,,,,])[a]=u,u=o,l*=2,a){case 0:r=n+l,s=i+l;break;case 1:n=r-l,s=i+l;break;case 2:r=n+l,i=s-l;break;case 3:n=r-l,i=s-l}this._root&&this._root.length&&(this._root=u)}return this._x0=n,this._y0=i,this._x1=r,this._y1=s,this},eW.data=function(){var t=[];return this.visit(function(e){if(!e.length)do t.push(e.data);while(e=e.next)}),t},eW.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},eW.find=function(t,e,n){var i,r,s,o,a,l,u,h=this._x0,c=this._y0,f=this._x1,d=this._y1,p=[],m=this._root;for(m&&p.push(new ez(m,h,c,f,d)),null==n?n=1/0:(h=t-n,c=e-n,f=t+n,d=e+n,n*=n);l=p.pop();)if((m=l.node)&&!((r=l.x0)>f)&&!((s=l.y0)>d)&&!((o=l.x1)<h)&&!((a=l.y1)<c))if(m.length){var v=(r+o)/2,y=(s+a)/2;p.push(new ez(m[3],v,y,o,a),new ez(m[2],r,y,v,a),new ez(m[1],v,s,o,y),new ez(m[0],r,s,v,y)),(u=(e>=y)<<1|t>=v)&&(l=p[p.length-1],p[p.length-1]=p[p.length-1-u],p[p.length-1-u]=l)}else{var g=t-this._x.call(null,m.data),x=e-this._y.call(null,m.data),w=g*g+x*x;if(w<n){var b=Math.sqrt(n=w);h=t-b,c=e-b,f=t+b,d=e+b,i=m.data}}return i},eW.remove=function(t){if(isNaN(s=+this._x.call(null,t))||isNaN(o=+this._y.call(null,t)))return this;var e,n,i,r,s,o,a,l,u,h,c,f,d=this._root,p=this._x0,m=this._y0,v=this._x1,y=this._y1;if(!d)return this;if(d.length)for(;;){if((u=s>=(a=(p+v)/2))?p=a:v=a,(h=o>=(l=(m+y)/2))?m=l:y=l,e=d,!(d=d[c=h<<1|u]))return this;if(!d.length)break;(e[c+1&3]||e[c+2&3]||e[c+3&3])&&(n=e,f=c)}for(;d.data!==t;)if(i=d,!(d=d.next))return this;return((r=d.next)&&delete d.next,i)?r?i.next=r:delete i.next:e?(r?e[c]=r:delete e[c],(d=e[0]||e[1]||e[2]||e[3])&&d===(e[3]||e[2]||e[1]||e[0])&&!d.length&&(n?n[f]=d:this._root=d)):this._root=r,this},eW.removeAll=function(t){for(var e=0,n=t.length;e<n;++e)this.remove(t[e]);return this},eW.root=function(){return this._root},eW.size=function(){var t=0;return this.visit(function(e){if(!e.length)do++t;while(e=e.next)}),t},eW.visit=function(t){var e,n,i,r,s,o,a=[],l=this._root;for(l&&a.push(new ez(l,this._x0,this._y0,this._x1,this._y1));e=a.pop();)if(!t(l=e.node,i=e.x0,r=e.y0,s=e.x1,o=e.y1)&&l.length){var u=(i+s)/2,h=(r+o)/2;(n=l[3])&&a.push(new ez(n,u,h,s,o)),(n=l[2])&&a.push(new ez(n,i,h,u,o)),(n=l[1])&&a.push(new ez(n,u,r,s,h)),(n=l[0])&&a.push(new ez(n,i,r,u,h))}return this},eW.visitAfter=function(t){var e,n=[],i=[];for(this._root&&n.push(new ez(this._root,this._x0,this._y0,this._x1,this._y1));e=n.pop();){var r=e.node;if(r.length){var s,o=e.x0,a=e.y0,l=e.x1,u=e.y1,h=(o+l)/2,c=(a+u)/2;(s=r[0])&&n.push(new ez(s,o,a,h,c)),(s=r[1])&&n.push(new ez(s,h,a,l,c)),(s=r[2])&&n.push(new ez(s,o,c,h,u)),(s=r[3])&&n.push(new ez(s,h,c,l,u))}i.push(e)}for(;e=i.pop();)t(e.node,e.x0,e.y0,e.x1,e.y1);return this},eW.x=function(t){return arguments.length?(this._x=t,this):this._x},eW.y=function(t){return arguments.length?(this._y=t,this):this._y};var e0=Math.PI*(3-Math.sqrt(5));function e1(t){let e;var n,i=1,r=.001,s=1-Math.pow(.001,1/300),o=0,a=.6,l=new Map,u=tp(f),h=Q("tick","end"),c=(e=1,()=>(e=(1664525*e+0x3c6ef35f)%0x100000000)/0x100000000);function f(){d(),h.call("tick",n),i<r&&(u.stop(),h.call("end",n))}function d(e){var r,u,h=t.length;void 0===e&&(e=1);for(var c=0;c<e;++c)for(i+=(o-i)*s,l.forEach(function(t){t(i)}),r=0;r<h;++r)null==(u=t[r]).fx?u.x+=u.vx*=a:(u.x=u.fx,u.vx=0),null==u.fy?u.y+=u.vy*=a:(u.y=u.fy,u.vy=0);return n}function p(){for(var e,n=0,i=t.length;n<i;++n){if((e=t[n]).index=n,null!=e.fx&&(e.x=e.fx),null!=e.fy&&(e.y=e.fy),isNaN(e.x)||isNaN(e.y)){var r=10*Math.sqrt(.5+n),s=n*e0;e.x=r*Math.cos(s),e.y=r*Math.sin(s)}(isNaN(e.vx)||isNaN(e.vy))&&(e.vx=e.vy=0)}}function m(e){return e.initialize&&e.initialize(t,c),e}return null==t&&(t=[]),p(),n={tick:d,restart:function(){return u.restart(f),n},stop:function(){return u.stop(),n},nodes:function(e){return arguments.length?(t=e,p(),l.forEach(m),n):t},alpha:function(t){return arguments.length?(i=+t,n):i},alphaMin:function(t){return arguments.length?(r=+t,n):r},alphaDecay:function(t){return arguments.length?(s=+t,n):+s},alphaTarget:function(t){return arguments.length?(o=+t,n):o},velocityDecay:function(t){return arguments.length?(a=1-t,n):1-a},randomSource:function(t){return arguments.length?(c=t,l.forEach(m),n):c},force:function(t,e){return arguments.length>1?(null==e?l.delete(t):l.set(t,m(e)),n):l.get(t)},find:function(e,n,i){var r,s,o,a,l,u=0,h=t.length;for(null==i?i=1/0:i*=i,u=0;u<h;++u)(o=(r=e-(a=t[u]).x)*r+(s=n-a.y)*s)<i&&(l=a,i=o);return l},on:function(t,e){return arguments.length>1?(h.on(t,e),n):h.on(t)}}}function e2(){var t,e,n,i,r,s=eY(-30),o=1,a=1/0,l=.81;function u(n){var r,s=t.length,o=eU(t,eQ,eJ).visitAfter(c);for(i=n,r=0;r<s;++r)e=t[r],o.visit(f)}function h(){if(t){var e,n,i=t.length;for(e=0,r=Array(i);e<i;++e)r[(n=t[e]).index]=+s(n,e,t)}}function c(t){var e,n,i,s,o,a=0,l=0;if(t.length){for(i=s=o=0;o<4;++o)(e=t[o])&&(n=Math.abs(e.value))&&(a+=e.value,l+=n,i+=n*e.x,s+=n*e.y);t.x=i/l,t.y=s/l}else{(e=t).x=e.data.x,e.y=e.data.y;do a+=r[e.data.index];while(e=e.next)}t.value=a}function f(t,s,u,h){if(!t.value)return!0;var c=t.x-e.x,f=t.y-e.y,d=h-s,p=c*c+f*f;if(d*d/l<p)return p<a&&(0===c&&(p+=(c=eH(n))*c),0===f&&(p+=(f=eH(n))*f),p<o&&(p=Math.sqrt(o*p)),e.vx+=c*t.value*i/p,e.vy+=f*t.value*i/p),!0;if(!t.length&&!(p>=a)){(t.data!==e||t.next)&&(0===c&&(p+=(c=eH(n))*c),0===f&&(p+=(f=eH(n))*f),p<o&&(p=Math.sqrt(o*p)));do t.data!==e&&(d=r[t.data.index]*i/p,e.vx+=c*d,e.vy+=f*d);while(t=t.next)}}return u.initialize=function(e,i){t=e,n=i,h()},u.strength=function(t){return arguments.length?(s="function"==typeof t?t:eY(+t),h(),u):s},u.distanceMin=function(t){return arguments.length?(o=t*t,u):Math.sqrt(o)},u.distanceMax=function(t){return arguments.length?(a=t*t,u):Math.sqrt(a)},u.theta=function(t){return arguments.length?(l=t*t,u):Math.sqrt(l)},u}function e5(t){var e,n,i,r=eY(.1);function s(t){for(var r,s=0,o=e.length;s<o;++s)r=e[s],r.vx+=(i[s]-r.x)*n[s]*t}function o(){if(e){var s,o=e.length;for(s=0,n=Array(o),i=Array(o);s<o;++s)n[s]=isNaN(i[s]=+t(e[s],s,e))?0:+r(e[s],s,e)}}return"function"!=typeof t&&(t=eY(null==t?0:+t)),s.initialize=function(t){e=t,o()},s.strength=function(t){return arguments.length?(r="function"==typeof t?t:eY(+t),o(),s):r},s.x=function(e){return arguments.length?(t="function"==typeof e?e:eY(+e),o(),s):t},s}function e3(t){var e,n,i,r=eY(.1);function s(t){for(var r,s=0,o=e.length;s<o;++s)r=e[s],r.vy+=(i[s]-r.y)*n[s]*t}function o(){if(e){var s,o=e.length;for(s=0,n=Array(o),i=Array(o);s<o;++s)n[s]=isNaN(i[s]=+t(e[s],s,e))?0:+r(e[s],s,e)}}return"function"!=typeof t&&(t=eY(null==t?0:+t)),s.initialize=function(t){e=t,o()},s.strength=function(t){return arguments.length?(r="function"==typeof t?t:eY(+t),o(),s):r},s.y=function(e){return arguments.length?(t="function"==typeof e?e:eY(+e),o(),s):t},s}let e4=Math.sqrt(50),e8=Math.sqrt(10),e9=Math.sqrt(2);function e6(t,e,n){let i,r,s,o=(e-t)/Math.max(0,n),a=Math.floor(Math.log10(o)),l=o/Math.pow(10,a),u=l>=e4?10:l>=e8?5:l>=e9?2:1;return(a<0?(i=Math.round(t*(s=Math.pow(10,-a)/u)),r=Math.round(e*s),i/s<t&&++i,r/s>e&&--r,s=-s):(i=Math.round(t/(s=Math.pow(10,a)*u)),r=Math.round(e/s),i*s<t&&++i,r*s>e&&--r),r<i&&.5<=n&&n<2)?e6(t,e,2*n):[i,r,s]}function e7(t,e,n){return e6(t*=1,e*=1,n*=1)[2]}function nt(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ne(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function nn(t){let e,n,i;function r(t,i,s=0,o=t.length){if(s<o){if(0!==e(i,i))return o;do{let e=s+o>>>1;0>n(t[e],i)?s=e+1:o=e}while(s<o)}return s}return 2!==t.length?(e=nt,n=(e,n)=>nt(t(e),n),i=(e,n)=>t(e)-n):(e=t===nt||t===ne?t:ni,n=t,i=t),{left:r,center:function(t,e,n=0,s=t.length){let o=r(t,e,n,s-1);return o>n&&i(t[o-1],e)>-i(t[o],e)?o-1:o},right:function(t,i,r=0,s=t.length){if(r<s){if(0!==e(i,i))return s;do{let e=r+s>>>1;0>=n(t[e],i)?r=e+1:s=e}while(r<s)}return r}}}function ni(){return 0}let nr=nn(nt),ns=nr.right;nr.left,nn(function(t){return null===t?NaN:+t}).center;function no(t,e){var n,i,r=typeof e;return null==e||"boolean"===r?ei(e):("number"===r?tS:"string"===r?(i=tG(e))?(e=i,es):eu:e instanceof tG?es:e instanceof Date?function(t,e){var n=new Date;return t*=1,e*=1,function(i){return n.setTime(t*(1-i)+e*i),n}}:!ArrayBuffer.isView(n=e)||n instanceof DataView?Array.isArray(e)?function(t,e){var n,i=e?e.length:0,r=t?Math.min(i,t.length):0,s=Array(r),o=Array(i);for(n=0;n<r;++n)s[n]=no(t[n],e[n]);for(;n<i;++n)o[n]=e[n];return function(t){for(n=0;n<r;++n)o[n]=s[n](t);return o}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var n,i={},r={};for(n in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)n in t?i[n]=no(t[n],e[n]):r[n]=e[n];return function(t){for(n in i)r[n]=i[n](t);return r}}:tS:function(t,e){e||(e=[]);var n,i=t?Math.min(e.length,t.length):0,r=e.slice();return function(s){for(n=0;n<i;++n)r[n]=t[n]*(1-s)+e[n]*s;return r}})(t,e)}function na(t,e){return t*=1,e*=1,function(n){return Math.round(t*(1-n)+e*n)}}function nl(t){return+t}var nu=[0,1];function nh(t){return t}function nc(t,e){var n;return(e-=t*=1)?function(n){return(n-t)/e}:(n=isNaN(e)?NaN:.5,function(){return n})}function nf(t,e,n){var i=t[0],r=t[1],s=e[0],o=e[1];return r<i?(i=nc(r,i),s=n(o,s)):(i=nc(i,r),s=n(s,o)),function(t){return s(i(t))}}function nd(t,e,n){var i=Math.min(t.length,e.length)-1,r=Array(i),s=Array(i),o=-1;for(t[i]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++o<i;)r[o]=nc(t[o],t[o+1]),s[o]=n(e[o],e[o+1]);return function(e){var n=ns(t,e,1,i)-1;return s[n](r[n](e))}}function np(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function nm(){var t,e,n,i,r,s,o=nu,a=nu,l=no,u=nh;function h(){var t,e,n,l=Math.min(o.length,a.length);return u!==nh&&(t=o[0],e=o[l-1],t>e&&(n=t,t=e,e=n),u=function(n){return Math.max(t,Math.min(e,n))}),i=l>2?nd:nf,r=s=null,c}function c(e){return null==e||isNaN(e*=1)?n:(r||(r=i(o.map(t),a,l)))(t(u(e)))}return c.invert=function(n){return u(e((s||(s=i(a,o.map(t),tS)))(n)))},c.domain=function(t){return arguments.length?(o=Array.from(t,nl),h()):o.slice()},c.range=function(t){return arguments.length?(a=Array.from(t),h()):a.slice()},c.rangeRound=function(t){return a=Array.from(t),l=na,h()},c.clamp=function(t){return arguments.length?(u=!!t||nh,h()):u!==nh},c.interpolate=function(t){return arguments.length?(l=t,h()):l},c.unknown=function(t){return arguments.length?(n=t,c):n},function(n,i){return t=n,e=i,h()}}function nv(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}var ny=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ng(t){var e;if(!(e=ny.exec(t)))throw Error("invalid format: "+t);return new nx({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function nx(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function nw(t,e){if((n=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var n,i=t.slice(0,n);return[i.length>1?i[0]+i.slice(2):i,+t.slice(n+1)]}function nb(t){return(t=nw(Math.abs(t)))?t[1]:NaN}function n_(t,e){var n=nw(t,e);if(!n)return t+"";var i=n[0],r=n[1];return r<0?"0."+Array(-r).join("0")+i:i.length>r+1?i.slice(0,r+1)+"."+i.slice(r+1):i+Array(r-i.length+2).join("0")}ng.prototype=nx.prototype,nx.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let nM={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>n_(100*t,e),r:n_,s:function(t,e){var n=nw(t,e);if(!n)return t+"";var i=n[0],r=n[1],s=r-(f=3*Math.max(-8,Math.min(8,Math.floor(r/3))))+1,o=i.length;return s===o?i:s>o?i+Array(s-o+1).join("0"):s>0?i.slice(0,s)+"."+i.slice(s):"0."+Array(1-s).join("0")+nw(t,Math.max(0,e+s-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function nA(t){return t}var nT=Array.prototype.map,nS=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function nP(t){var e=t.domain;return t.ticks=function(t){var n=e();return function(t,e,n){if(e*=1,t*=1,!((n*=1)>0))return[];if(t===e)return[t];let i=e<t,[r,s,o]=i?e6(e,t,n):e6(t,e,n);if(!(s>=r))return[];let a=s-r+1,l=Array(a);if(i)if(o<0)for(let t=0;t<a;++t)l[t]=-((s-t)/o);else for(let t=0;t<a;++t)l[t]=(s-t)*o;else if(o<0)for(let t=0;t<a;++t)l[t]=-((r+t)/o);else for(let t=0;t<a;++t)l[t]=(r+t)*o;return l}(n[0],n[n.length-1],null==t?10:t)},t.tickFormat=function(t,n){var i=e();return function(t,e,n,i){var r,s,o,a=function(t,e,n){e*=1,t*=1,n*=1;let i=e<t,r=i?e7(e,t,n):e7(t,e,n);return(i?-1:1)*(r<0?-(1/r):r)}(t,e,n);switch((i=ng(null==i?",f":i)).type){case"s":var l=Math.max(Math.abs(t),Math.abs(e));return null!=i.precision||isNaN(o=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(nb(l)/3)))-nb(Math.abs(a))))||(i.precision=o),m(i,l);case"":case"e":case"g":case"p":case"r":null!=i.precision||isNaN(o=Math.max(0,nb(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(r=Math.abs(r=a)))-nb(r))+1)||(i.precision=o-("e"===i.type));break;case"f":case"%":null!=i.precision||isNaN(o=Math.max(0,-nb(Math.abs(a))))||(i.precision=o-("%"===i.type)*2)}return p(i)}(i[0],i[i.length-1],null==t?10:t,n)},t.nice=function(n){null==n&&(n=10);var i,r,s=e(),o=0,a=s.length-1,l=s[o],u=s[a],h=10;for(u<l&&(r=l,l=u,u=r,r=o,o=a,a=r);h-- >0;){if((r=e7(l,u,n))===i)return s[o]=l,s[a]=u,e(s);if(r>0)l=Math.floor(l/r)*r,u=Math.ceil(u/r)*r;else if(r<0)l=Math.ceil(l*r)/r,u=Math.floor(u*r)/r;else break;i=r}return t},t}function nk(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function nE(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function nV(t){return t<0?-t*t:t*t}function nC(){return(function t(){var e,n,i,r=(n=(e=nm())(nh,nh),i=1,n.exponent=function(t){return arguments.length?1==(i=+t)?e(nh,nh):.5===i?e(nE,nV):e(nk(i),nk(1/i)):i},nP(n));return r.copy=function(){return np(r,t()).exponent(r.exponent())},nv.apply(r,arguments),r}).apply(null,arguments).exponent(.5)}function nD(t){return"string"==typeof t?new K([[document.querySelector(t)]],[document.documentElement]):new K([[t]],H)}p=(d=function(t){var e,n,i,r=void 0===t.grouping||void 0===t.thousands?nA:(e=nT.call(t.grouping,Number),n=t.thousands+"",function(t,i){for(var r=t.length,s=[],o=0,a=e[0],l=0;r>0&&a>0&&(l+a+1>i&&(a=Math.max(1,i-l)),s.push(t.substring(r-=a,r+a)),!((l+=a+1)>i));)a=e[o=(o+1)%e.length];return s.reverse().join(n)}),s=void 0===t.currency?"":t.currency[0]+"",o=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?nA:(i=nT.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return i[+t]})}),u=void 0===t.percent?"%":t.percent+"",h=void 0===t.minus?"−":t.minus+"",c=void 0===t.nan?"NaN":t.nan+"";function d(t){var e=(t=ng(t)).fill,n=t.align,i=t.sign,d=t.symbol,p=t.zero,m=t.width,v=t.comma,y=t.precision,g=t.trim,x=t.type;"n"===x?(v=!0,x="g"):nM[x]||(void 0===y&&(y=12),g=!0,x="g"),(p||"0"===e&&"="===n)&&(p=!0,e="0",n="=");var w="$"===d?s:"#"===d&&/[boxX]/.test(x)?"0"+x.toLowerCase():"",b="$"===d?o:/[%p]/.test(x)?u:"",_=nM[x],M=/[defgprs%]/.test(x);function A(t){var s,o,u,d=w,A=b;if("c"===x)A=_(t)+A,t="";else{var T=(t*=1)<0||1/t<0;if(t=isNaN(t)?c:_(Math.abs(t),y),g&&(t=function(t){t:for(var e,n=t.length,i=1,r=-1;i<n;++i)switch(t[i]){case".":r=e=i;break;case"0":0===r&&(r=i),e=i;break;default:if(!+t[i])break t;r>0&&(r=0)}return r>0?t.slice(0,r)+t.slice(e+1):t}(t)),T&&0==+t&&"+"!==i&&(T=!1),d=(T?"("===i?i:h:"-"===i||"("===i?"":i)+d,A=("s"===x?nS[8+f/3]:"")+A+(T&&"("===i?")":""),M){for(s=-1,o=t.length;++s<o;)if(48>(u=t.charCodeAt(s))||u>57){A=(46===u?a+t.slice(s+1):t.slice(s))+A,t=t.slice(0,s);break}}}v&&!p&&(t=r(t,1/0));var S=d.length+t.length+A.length,P=S<m?Array(m-S+1).join(e):"";switch(v&&p&&(t=r(P+t,P.length?m-A.length:1/0),P=""),n){case"<":t=d+t+A+P;break;case"=":t=d+P+t+A;break;case"^":t=P.slice(0,S=P.length>>1)+d+t+A+P.slice(S);break;default:t=P+d+t+A}return l(t)}return y=void 0===y?6:/[gprs]/.test(x)?Math.max(1,Math.min(21,y)):Math.max(0,Math.min(20,y)),A.toString=function(){return t+""},A}return{format:d,formatPrefix:function(t,e){var n=d(((t=ng(t)).type="f",t)),i=3*Math.max(-8,Math.min(8,Math.floor(nb(e)/3))),r=Math.pow(10,-i),s=nS[8+i/3];return function(t){return n(r*t)+s}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,m=d.formatPrefix;let nN={capture:!0,passive:!1};function nR(t){t.preventDefault(),t.stopImmediatePropagation()}function nj(t){return((t=Math.exp(t))+1/t)/2}let nL=function t(e,n,i){function r(t,r){var s,o,a=t[0],l=t[1],u=t[2],h=r[0],c=r[1],f=r[2],d=h-a,p=c-l,m=d*d+p*p;if(m<1e-12)o=Math.log(f/u)/e,s=function(t){return[a+t*d,l+t*p,u*Math.exp(e*t*o)]};else{var v=Math.sqrt(m),y=(f*f-u*u+i*m)/(2*u*n*v),g=(f*f-u*u-i*m)/(2*f*n*v),x=Math.log(Math.sqrt(y*y+1)-y);o=(Math.log(Math.sqrt(g*g+1)-g)-x)/e,s=function(t){var i,r,s=t*o,h=nj(x),c=u/(n*v)*(h*(((i=Math.exp(2*(i=e*s+x)))-1)/(i+1))-((r=Math.exp(r=x))-1/r)/2);return[a+c*d,l+c*p,u*h/nj(e*s+x)]}}return s.duration=1e3*o*e/Math.SQRT2,s}return r.rho=function(e){var n=Math.max(.001,+e),i=n*n;return t(n,i,i*i)},r}(Math.SQRT2,2,4);function nB(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var i=n.createSVGPoint();return i.x=t.clientX,i.y=t.clientY,[(i=i.matrixTransform(e.getScreenCTM().inverse())).x,i.y]}if(e.getBoundingClientRect){var r=e.getBoundingClientRect();return[t.clientX-r.left-e.clientLeft,t.clientY-r.top-e.clientTop]}}return[t.pageX,t.pageY]}let nF=t=>()=>t;function nO(t,{sourceEvent:e,target:n,transform:i,dispatch:r}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:i,enumerable:!0,configurable:!0},_:{value:r}})}function nz(t,e,n){this.k=t,this.x=e,this.y=n}nz.prototype={constructor:nz,scale:function(t){return 1===t?this:new nz(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new nz(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var n$=new nz(1,0,0);function nI(t){t.stopImmediatePropagation()}function nU(t){t.preventDefault(),t.stopImmediatePropagation()}function nq(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function nX(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function nW(){return this.__zoom||n$}function nY(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function nH(){return navigator.maxTouchPoints||"ontouchstart"in this}function nK(t,e,n){var i=t.invertX(e[0][0])-n[0][0],r=t.invertX(e[1][0])-n[1][0],s=t.invertY(e[0][1])-n[0][1],o=t.invertY(e[1][1])-n[1][1];return t.translate(r>i?(i+r)/2:Math.min(0,i)||Math.max(0,r),o>s?(s+o)/2:Math.min(0,s)||Math.max(0,o))}function nG(){var t,e,n,i=nq,r=nX,s=nK,o=nY,a=nH,l=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],h=250,c=nL,f=Q("start","zoom","end"),d=0,p=10;function m(t){t.property("__zoom",nW).on("wheel.zoom",_,{passive:!1}).on("mousedown.zoom",M).on("dblclick.zoom",A).filter(a).on("touchstart.zoom",T).on("touchmove.zoom",S).on("touchend.zoom touchcancel.zoom",P).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function v(t,e){return(e=Math.max(l[0],Math.min(l[1],e)))===t.k?t:new nz(e,t.x,t.y)}function y(t,e,n){var i=e[0]-n[0]*t.k,r=e[1]-n[1]*t.k;return i===t.x&&r===t.y?t:new nz(t.k,i,r)}function g(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function x(t,e,n,i){t.on("start.zoom",function(){w(this,arguments).event(i).start()}).on("interrupt.zoom end.zoom",function(){w(this,arguments).event(i).end()}).tween("zoom",function(){var t=arguments,s=w(this,t).event(i),o=r.apply(this,t),a=null==n?g(o):"function"==typeof n?n.apply(this,t):n,l=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),u=this.__zoom,h="function"==typeof e?e.apply(this,t):e,f=c(u.invert(a).concat(l/u.k),h.invert(a).concat(l/h.k));return function(t){if(1===t)t=h;else{var e=f(t),n=l/e[2];t=new nz(n,a[0]-e[0]*n,a[1]-e[1]*n)}s.zoom(null,t)}})}function w(t,e,n){return!n&&t.__zooming||new b(t,e)}function b(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=r.apply(t,e),this.taps=0}function _(t,...e){if(i.apply(this,arguments)){var n=w(this,e).event(t),r=this.__zoom,a=Math.max(l[0],Math.min(l[1],r.k*Math.pow(2,o.apply(this,arguments)))),h=nB(t);if(n.wheel)(n.mouse[0][0]!==h[0]||n.mouse[0][1]!==h[1])&&(n.mouse[1]=r.invert(n.mouse[0]=h)),clearTimeout(n.wheel);else{if(r.k===a)return;n.mouse=[h,r.invert(h)],tT(this),n.start()}nU(t),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",s(y(v(r,a),n.mouse[0],n.mouse[1]),n.extent,u))}}function M(t,...e){if(!n&&i.apply(this,arguments)){var r,o,a,l=t.currentTarget,h=w(this,e,!0).event(t),c=nD(t.view).on("mousemove.zoom",function(t){if(nU(t),!h.moved){var e=t.clientX-p,n=t.clientY-m;h.moved=e*e+n*n>d}h.event(t).zoom("mouse",s(y(h.that.__zoom,h.mouse[0]=nB(t,l),h.mouse[1]),h.extent,u))},!0).on("mouseup.zoom",function(t){var e,n,i,r;c.on("mousemove.zoom mouseup.zoom",null),e=t.view,n=h.moved,i=e.document.documentElement,r=nD(e).on("dragstart.drag",null),n&&(r.on("click.drag",nR,nN),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in i?r.on("selectstart.drag",null):(i.style.MozUserSelect=i.__noselect,delete i.__noselect),nU(t),h.event(t).end()},!0),f=nB(t,l),p=t.clientX,m=t.clientY;o=(r=t.view).document.documentElement,a=nD(r).on("dragstart.drag",nR,nN),"onselectstart"in o?a.on("selectstart.drag",nR,nN):(o.__noselect=o.style.MozUserSelect,o.style.MozUserSelect="none"),nI(t),h.mouse=[f,this.__zoom.invert(f)],tT(this),h.start()}}function A(t,...e){if(i.apply(this,arguments)){var n=this.__zoom,o=nB(t.changedTouches?t.changedTouches[0]:t,this),a=n.invert(o),l=n.k*(t.shiftKey?.5:2),c=s(y(v(n,l),o,a),r.apply(this,e),u);nU(t),h>0?nD(this).transition().duration(h).call(x,c,o,t):nD(this).call(m.transform,c,o,t)}}function T(n,...r){if(i.apply(this,arguments)){var s,o,a,l,u=n.touches,h=u.length,c=w(this,r,n.changedTouches.length===h).event(n);for(nI(n),o=0;o<h;++o)l=[l=nB(a=u[o],this),this.__zoom.invert(l),a.identifier],c.touch0?c.touch1||c.touch0[2]===l[2]||(c.touch1=l,c.taps=0):(c.touch0=l,s=!0,c.taps=1+!!t);t&&(t=clearTimeout(t)),s&&(c.taps<2&&(e=l[0],t=setTimeout(function(){t=null},500)),tT(this),c.start())}}function S(t,...e){if(this.__zooming){var n,i,r,o,a=w(this,e).event(t),l=t.changedTouches,h=l.length;for(nU(t),n=0;n<h;++n)r=nB(i=l[n],this),a.touch0&&a.touch0[2]===i.identifier?a.touch0[0]=r:a.touch1&&a.touch1[2]===i.identifier&&(a.touch1[0]=r);if(i=a.that.__zoom,a.touch1){var c=a.touch0[0],f=a.touch0[1],d=a.touch1[0],p=a.touch1[1],m=(m=d[0]-c[0])*m+(m=d[1]-c[1])*m,g=(g=p[0]-f[0])*g+(g=p[1]-f[1])*g;i=v(i,Math.sqrt(m/g)),r=[(c[0]+d[0])/2,(c[1]+d[1])/2],o=[(f[0]+p[0])/2,(f[1]+p[1])/2]}else{if(!a.touch0)return;r=a.touch0[0],o=a.touch0[1]}a.zoom("touch",s(y(i,r,o),a.extent,u))}}function P(t,...i){if(this.__zooming){var r,s,o=w(this,i).event(t),a=t.changedTouches,l=a.length;for(nI(t),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),r=0;r<l;++r)s=a[r],o.touch0&&o.touch0[2]===s.identifier?delete o.touch0:o.touch1&&o.touch1[2]===s.identifier&&delete o.touch1;if(o.touch1&&!o.touch0&&(o.touch0=o.touch1,delete o.touch1),o.touch0)o.touch0[1]=this.__zoom.invert(o.touch0[0]);else if(o.end(),2===o.taps&&(s=nB(s,this),Math.hypot(e[0]-s[0],e[1]-s[1])<p)){var u=nD(this).on("dblclick.zoom");u&&u.apply(this,arguments)}}}return m.transform=function(t,e,n,i){var r=t.selection?t.selection():t;r.property("__zoom",nW),t!==r?x(t,e,n,i):r.interrupt().each(function(){w(this,arguments).event(i).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()})},m.scaleBy=function(t,e,n,i){m.scaleTo(t,function(){var t=this.__zoom.k,n="function"==typeof e?e.apply(this,arguments):e;return t*n},n,i)},m.scaleTo=function(t,e,n,i){m.transform(t,function(){var t=r.apply(this,arguments),i=this.__zoom,o=null==n?g(t):"function"==typeof n?n.apply(this,arguments):n,a=i.invert(o),l="function"==typeof e?e.apply(this,arguments):e;return s(y(v(i,l),o,a),t,u)},n,i)},m.translateBy=function(t,e,n,i){m.transform(t,function(){return s(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),u)},null,i)},m.translateTo=function(t,e,n,i,o){m.transform(t,function(){var t=r.apply(this,arguments),o=this.__zoom,a=null==i?g(t):"function"==typeof i?i.apply(this,arguments):i;return s(n$.translate(a[0],a[1]).scale(o.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,u)},i,o)},b.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=nD(this.that).datum();f.call(t,this.that,new nO(t,{sourceEvent:this.sourceEvent,target:m,type:t,transform:this.that.__zoom,dispatch:f}),e)}},m.wheelDelta=function(t){return arguments.length?(o="function"==typeof t?t:nF(+t),m):o},m.filter=function(t){return arguments.length?(i="function"==typeof t?t:nF(!!t),m):i},m.touchable=function(t){return arguments.length?(a="function"==typeof t?t:nF(!!t),m):a},m.extent=function(t){return arguments.length?(r="function"==typeof t?t:nF([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),m):r},m.scaleExtent=function(t){return arguments.length?(l[0]=+t[0],l[1]=+t[1],m):[l[0],l[1]]},m.translateExtent=function(t){return arguments.length?(u[0][0]=+t[0][0],u[1][0]=+t[1][0],u[0][1]=+t[0][1],u[1][1]=+t[1][1],m):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},m.constrain=function(t){return arguments.length?(s=t,m):s},m.duration=function(t){return arguments.length?(h=+t,m):h},m.interpolate=function(t){return arguments.length?(c=t,m):c},m.on=function(){var t=f.on.apply(f,arguments);return t===f?m:t},m.clickDistance=function(t){return arguments.length?(d=(t*=1)*t,m):Math.sqrt(d)},m.tapDistance=function(t){return arguments.length?(p=+t,m):p},m}nz.prototype},6932:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},6983:(t,e,n)=>{n.d(e,{G:()=>i});function i(t){return"object"==typeof t&&null!==t}},7351:(t,e,n)=>{n.d(e,{s:()=>r});var i=n(6983);function r(t){return(0,i.G)(t)&&"offsetHeight"in t}},7494:(t,e,n)=>{n.d(e,{E:()=>r});var i=n(2115);let r=n(8972).B?i.useLayoutEffect:i.useEffect},7924:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8500:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(9946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},8972:(t,e,n)=>{n.d(e,{B:()=>i});let i="undefined"!=typeof window},9946:(t,e,n)=>{n.d(e,{A:()=>c});var i=n(2115);let r=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,n)=>n?n.toUpperCase():e.toLowerCase()),o=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.filter((t,e,n)=>!!t&&""!==t.trim()&&n.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,i.forwardRef)((t,e)=>{let{color:n="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:h="",children:c,iconNode:f,...d}=t;return(0,i.createElement)("svg",{ref:e,...u,width:r,height:r,stroke:n,strokeWidth:o?24*Number(s)/Number(r):s,className:a("lucide",h),...!c&&!l(d)&&{"aria-hidden":"true"},...d},[...f.map(t=>{let[e,n]=t;return(0,i.createElement)(e,n)}),...Array.isArray(c)?c:[c]])}),c=(t,e)=>{let n=(0,i.forwardRef)((n,s)=>{let{className:l,...u}=n;return(0,i.createElement)(h,{ref:s,iconNode:e,className:a("lucide-".concat(r(o(t))),"lucide-".concat(t),l),...u})});return n.displayName=o(t),n}}}]);