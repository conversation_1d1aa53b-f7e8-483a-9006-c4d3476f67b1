'use client';

import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { CryptoCurrency, BubbleData } from '@/types';

interface BubbleChartProps {
  data: CryptoCurrency[];
  width?: number;
  height?: number;
  onBubbleClick?: (crypto: CryptoCurrency) => void;
  onBubbleHover?: (crypto: CryptoCurrency | null) => void;
}

export const BubbleChart: React.FC<BubbleChartProps> = ({
  data,
  width = 1200,
  height = 800,
  onBubbleClick,
  onBubbleHover
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [hoveredBubble, setHoveredBubble] = useState<CryptoCurrency | null>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create scales
    const maxMarketCap = d3.max(data, d => d.marketCap) || 1;
    const minMarketCap = d3.min(data, d => d.marketCap) || 1;

    // Calculate total area and optimal bubble sizes to fill screen
    const totalArea = width * height;
    const bubbleCount = data.length;
    const averageArea = totalArea / bubbleCount * 0.7; // Use 70% of available space
    const averageRadius = Math.sqrt(averageArea / Math.PI);

    // Radius scale - pack bubbles to fill screen efficiently
    const radiusScale = d3.scaleSqrt()
      .domain([minMarketCap, maxMarketCap])
      .range([averageRadius * 0.3, averageRadius * 2.5]); // More aggressive sizing

    // Color scale for price changes with more vibrant colors
    const colorScale = d3.scaleLinear<string>()
      .domain([-15, -5, 0, 5, 15])
      .range(['#dc2626', '#f87171', '#6b7280', '#34d399', '#059669'])
      .clamp(true);

    // Prepare bubble data
    const bubbleData: BubbleData[] = data.map(crypto => ({
      ...crypto,
      x: 0,
      y: 0,
      r: radiusScale(crypto.marketCap),
      color: colorScale(crypto.change24h)
    }));

    // Create force simulation for tight packing like CryptoBubbles
    const simulation = d3.forceSimulation<BubbleData>(bubbleData)
      .force('charge', d3.forceManyBody().strength(-30)) // Reduced repulsion for tighter packing
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide<BubbleData>().radius(d => d.r + 1).strength(1)) // Tight collision with minimal padding
      .force('x', d3.forceX<BubbleData>(width / 2).strength(0.1)) // Stronger centering to fill screen
      .force('y', d3.forceY<BubbleData>(height / 2).strength(0.1))
      // Add boundary forces to keep bubbles on screen
      .force('boundary', () => {
        bubbleData.forEach(d => {
          const padding = d.r + 5;
          d.x = Math.max(padding, Math.min(width - padding, d.x || width / 2));
          d.y = Math.max(padding, Math.min(height - padding, d.y || height / 2));
        });
      })
      .alphaDecay(0.01) // Slower decay for better settling
      .velocityDecay(0.5); // Higher friction for stability

    // Create container group
    const container = svg.append('g');

    // Create bubbles with entrance animation
    const bubbles = container.selectAll('.bubble')
      .data(bubbleData)
      .enter()
      .append('g')
      .attr('class', 'bubble')
      .style('cursor', 'pointer')
      .style('opacity', 0);

    // Add circles with gradient effects
    const defs = svg.append('defs');

    // Create gradients for each bubble
    bubbles.each(function(d, i) {
      const gradient = defs.append('radialGradient')
        .attr('id', `gradient-${i}`)
        .attr('cx', '30%')
        .attr('cy', '30%');

      gradient.append('stop')
        .attr('offset', '0%')
        .attr('stop-color', d3.color(d.color)?.brighter(0.5)?.toString() || d.color);

      gradient.append('stop')
        .attr('offset', '100%')
        .attr('stop-color', d.color);
    });

    bubbles.append('circle')
      .attr('r', 0)
      .attr('fill', (d, i) => `url(#gradient-${i})`)
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 2)
      .attr('opacity', 0.85)
      .attr('filter', 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))')
      .transition()
      .duration(1000)
      .delay((d, i) => i * 50)
      .attr('r', d => d.r)
      .on('end', function() {
        if (this.parentNode) {
          d3.select(this.parentNode as Element).style('opacity', 1);
        }
      });

    // Enhanced hover effects with proper transform handling
    bubbles.on('mouseover', function(event, d) {
      const circle = d3.select(this).select('circle');

      // Cancel any ongoing transitions to prevent conflicts
      circle.interrupt();

      // Store the current scale for this bubble
      d.hoverScale = 1.1;

      circle.transition()
        .duration(200)
        .attr('opacity', 1)
        .attr('stroke-width', 4)
        .attr('stroke', '#fbbf24');

      // Bring to front by moving to end of parent (proper SVG z-ordering)
      const parent = this.parentNode;
      if (parent) {
        parent.appendChild(this);
      }

      setHoveredBubble(d);
      onBubbleHover?.(d);
    })
    .on('mouseout', function(event, d) {
      const circle = d3.select(this).select('circle');

      // Cancel any ongoing transitions to prevent conflicts
      circle.interrupt();

      // Reset the scale for this bubble
      d.hoverScale = 1;

      circle.transition()
        .duration(200)
        .attr('opacity', 0.85)
        .attr('stroke-width', 2)
        .attr('stroke', '#ffffff');

      setHoveredBubble(null);
      onBubbleHover?.(null);
    })
    .on('click', function(event, d) {
      // Click animation with proper transform handling
      // Store original scale
      const originalScale = d.hoverScale || 1;

      // Animate click effect
      d.clickScale = 0.95;
      setTimeout(() => {
        d.clickScale = originalScale * 1.1;
        setTimeout(() => {
          d.clickScale = originalScale;
        }, 150);
      }, 150);

      onBubbleClick?.(d);
    });

    // Add symbol text - show for all bubbles with adaptive sizing
    bubbles.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '-0.1em')
      .attr('font-size', d => Math.max(8, Math.min(d.r * 0.4, 20)))
      .attr('font-weight', 'bold')
      .attr('fill', '#ffffff')
      .attr('pointer-events', 'none')
      .style('text-shadow', '2px 2px 4px rgba(0,0,0,0.9)')
      .style('font-family', 'system-ui, -apple-system, sans-serif')
      .style('dominant-baseline', 'central')
      .text(d => d.symbol);

    // Add percentage text - show for all bubbles
    bubbles.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '0.8em')
      .attr('font-size', d => Math.max(6, Math.min(d.r * 0.3, 16)))
      .attr('font-weight', '600')
      .attr('fill', '#ffffff')
      .attr('pointer-events', 'none')
      .style('text-shadow', '2px 2px 4px rgba(0,0,0,0.9)')
      .style('font-family', 'system-ui, -apple-system, sans-serif')
      .style('dominant-baseline', 'central')
      .text(d => `${d.change24h >= 0 ? '+' : ''}${d.change24h.toFixed(2)}%`);

    // Update positions on simulation tick with proper scaling
    simulation.on('tick', () => {
      bubbles.attr('transform', d => {
        const scale = d.clickScale || d.hoverScale || 1;
        return `translate(${d.x},${d.y}) scale(${scale})`;
      });
    });

    // Remove zoom functionality - bubbles should fill the screen

    // Cleanup
    return () => {
      simulation.stop();
    };
  }, [data, width, height, onBubbleClick, onBubbleHover]);

  return (
    <div className="relative">
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="bg-gradient-to-br from-gray-900 to-gray-800"
        style={{ display: 'block' }}
      />
      
      {/* Enhanced Tooltip */}
      {hoveredBubble && (
        <div className="absolute top-4 left-4 bg-white p-5 rounded-xl shadow-2xl border-2 border-gray-100 z-20 min-w-72 max-w-80 backdrop-blur-sm bg-white/95">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm"
                 style={{ backgroundColor: hoveredBubble.change24h >= 0 ? '#22c55e' : '#ef4444' }}>
              {hoveredBubble.symbol.charAt(0)}
            </div>
            <div>
              <h3 className="font-bold text-lg text-gray-900">{hoveredBubble.name}</h3>
              <span className="text-gray-500 text-sm">({hoveredBubble.symbol}) • Rank #{hoveredBubble.rank}</span>
            </div>
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Price:</span>
              <span className="font-bold text-lg">
                ${hoveredBubble.price < 1 ? hoveredBubble.price.toFixed(4) : hoveredBubble.price.toLocaleString()}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600">Market Cap:</span>
              <span className="font-semibold">
                {hoveredBubble.marketCap >= 1e9
                  ? `$${(hoveredBubble.marketCap / 1e9).toFixed(2)}B`
                  : `$${(hoveredBubble.marketCap / 1e6).toFixed(2)}M`
                }
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600">24h Volume:</span>
              <span className="font-semibold">
                {hoveredBubble.volume24h >= 1e9
                  ? `$${(hoveredBubble.volume24h / 1e9).toFixed(2)}B`
                  : `$${(hoveredBubble.volume24h / 1e6).toFixed(2)}M`
                }
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600">24h Change:</span>
              <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${
                hoveredBubble.change24h >= 0
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
              }`}>
                <span>{hoveredBubble.change24h >= 0 ? '↗' : '↘'}</span>
                <span>{hoveredBubble.change24h >= 0 ? '+' : ''}{hoveredBubble.change24h.toFixed(2)}%</span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600">7d Change:</span>
              <span className={`font-semibold ${hoveredBubble.change7d >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {hoveredBubble.change7d >= 0 ? '+' : ''}{hoveredBubble.change7d.toFixed(2)}%
              </span>
            </div>

            <div className="pt-2 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Category:</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                  {hoveredBubble.category}
                </span>
              </div>
            </div>
          </div>

          <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-500 text-center">
            Click bubble for detailed view
          </div>
        </div>
      )}
    </div>
  );
};
