"use client";

import React from "react";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  text?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "md",
  text = "Loading...",
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12",
  };

  return (
    <div
      className="flex flex-col items-center justify-center p-8"
      data-oid="6gmp8xi"
    >
      <div
        className={`${sizeClasses[size]} animate-spin rounded-full border-4 border-gray-300 border-t-blue-600`}
        data-oid="o81jg6y"
      ></div>
      {text && (
        <p className="mt-4 text-gray-600 text-sm" data-oid="ovy93vh">
          {text}
        </p>
      )}
    </div>
  );
};

export const BubbleChartSkeleton: React.FC = () => {
  return (
    <div
      className="w-full h-[700px] bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg border border-gray-200 flex items-center justify-center"
      data-oid="n18j76x"
    >
      <div className="text-center" data-oid="q_ecqx.">
        <div
          className="w-16 h-16 animate-spin rounded-full border-4 border-gray-600 border-t-blue-400 mx-auto mb-4"
          data-oid="4jdehoc"
        ></div>
        <p className="text-white text-lg font-medium" data-oid=":f8jvvv">
          Loading cryptocurrency data...
        </p>
        <p className="text-gray-400 text-sm mt-2" data-oid="h0lsp4m">
          Preparing interactive bubble chart
        </p>
      </div>
    </div>
  );
};
