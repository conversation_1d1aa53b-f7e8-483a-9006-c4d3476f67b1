{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/BubbleChart.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as d3 from 'd3';\nimport { CryptoCurrency, BubbleData } from '@/types';\n\ninterface BubbleChartProps {\n  data: CryptoCurrency[];\n  width?: number;\n  height?: number;\n  onBubbleClick?: (crypto: CryptoCurrency) => void;\n  onBubbleHover?: (crypto: CryptoCurrency | null) => void;\n}\n\nexport const BubbleChart: React.FC<BubbleChartProps> = ({\n  data,\n  width = 1200,\n  height = 800,\n  onBubbleClick,\n  onBubbleHover\n}) => {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [hoveredBubble, setHoveredBubble] = useState<CryptoCurrency | null>(null);\n\n  useEffect(() => {\n    if (!svgRef.current || !data.length) return;\n\n    const svg = d3.select(svgRef.current);\n    svg.selectAll('*').remove();\n\n    // Create scales\n    const maxMarketCap = d3.max(data, d => d.marketCap) || 1;\n    const minMarketCap = d3.min(data, d => d.marketCap) || 1;\n\n    // Calculate optimal bubble sizes to fill screen without overlapping\n    const totalArea = width * height;\n    const bubbleCount = data.length;\n    const averageArea = totalArea / bubbleCount * 0.5; // Use 50% of available space for non-overlapping\n    const averageRadius = Math.sqrt(averageArea / Math.PI);\n\n    // Radius scale - conservative sizing to prevent overlap\n    const radiusScale = d3.scaleSqrt()\n      .domain([minMarketCap, maxMarketCap])\n      .range([averageRadius * 0.4, averageRadius * 1.8]); // More conservative sizing\n\n    // Color scale for price changes with more vibrant colors\n    const colorScale = d3.scaleLinear<string>()\n      .domain([-15, -5, 0, 5, 15])\n      .range(['#dc2626', '#f87171', '#6b7280', '#34d399', '#059669'])\n      .clamp(true);\n\n    // Prepare bubble data\n    const bubbleData: BubbleData[] = data.map(crypto => ({\n      ...crypto,\n      x: 0,\n      y: 0,\n      r: radiusScale(crypto.marketCap),\n      color: colorScale(crypto.change24h)\n    }));\n\n    // Create force simulation for non-overlapping bubbles like CryptoBubbles\n    const simulation = d3.forceSimulation<BubbleData>(bubbleData)\n      .force('charge', d3.forceManyBody().strength(-50)) // Moderate repulsion to prevent overlap\n      .force('center', d3.forceCenter(width / 2, height / 2))\n      .force('collision', d3.forceCollide<BubbleData>()\n        .radius(d => d.r + 3) // Small padding to prevent overlap\n        .strength(1) // Strong collision detection\n        .iterations(3)) // Multiple iterations for better collision resolution\n      .force('x', d3.forceX<BubbleData>(width / 2).strength(0.05)) // Gentle centering\n      .force('y', d3.forceY<BubbleData>(height / 2).strength(0.05))\n      // Add boundary forces to keep bubbles on screen\n      .force('boundary', () => {\n        bubbleData.forEach(d => {\n          const padding = d.r + 5;\n          d.x = Math.max(padding, Math.min(width - padding, d.x || width / 2));\n          d.y = Math.max(padding, Math.min(height - padding, d.y || height / 2));\n        });\n      })\n      .alphaDecay(0.005) // Very slow decay for thorough settling\n      .velocityDecay(0.6); // Higher friction for stability\n\n    // Create container group\n    const container = svg.append('g');\n\n    // Create bubbles with entrance animation\n    const bubbles = container.selectAll('.bubble')\n      .data(bubbleData)\n      .enter()\n      .append('g')\n      .attr('class', 'bubble')\n      .style('cursor', 'pointer')\n      .style('opacity', 0);\n\n    // Add circles with gradient effects and inner glow\n    const defs = svg.append('defs');\n\n    // Create inner glow filter\n    const glowFilter = defs.append('filter')\n      .attr('id', 'inner-glow')\n      .attr('x', '-50%')\n      .attr('y', '-50%')\n      .attr('width', '200%')\n      .attr('height', '200%');\n\n    // Create the inner glow effect\n    glowFilter.append('feGaussianBlur')\n      .attr('stdDeviation', '3')\n      .attr('result', 'coloredBlur');\n\n    const feMerge = glowFilter.append('feMerge');\n    feMerge.append('feMergeNode').attr('in', 'coloredBlur');\n    feMerge.append('feMergeNode').attr('in', 'SourceGraphic');\n\n    // Create drop shadow filter\n    const shadowFilter = defs.append('filter')\n      .attr('id', 'drop-shadow')\n      .attr('x', '-50%')\n      .attr('y', '-50%')\n      .attr('width', '200%')\n      .attr('height', '200%');\n\n    shadowFilter.append('feDropShadow')\n      .attr('dx', '2')\n      .attr('dy', '2')\n      .attr('stdDeviation', '4')\n      .attr('flood-color', 'rgba(0,0,0,0.4)');\n\n    // Create transparent gradients for each bubble with inner glow\n    bubbles.each(function(d, i) {\n      const gradient = defs.append('radialGradient')\n        .attr('id', `gradient-${i}`)\n        .attr('cx', '50%')\n        .attr('cy', '50%');\n\n      // Create transparent bubble with inner glow effect\n      const baseColor = d3.color(d.color);\n\n      // Inner glow (bright center)\n      gradient.append('stop')\n        .attr('offset', '0%')\n        .attr('stop-color', baseColor?.brighter(1.5)?.toString() || d.color)\n        .attr('stop-opacity', '0.3');\n\n      // Mid section (main color)\n      gradient.append('stop')\n        .attr('offset', '60%')\n        .attr('stop-color', d.color)\n        .attr('stop-opacity', '0.15');\n\n      // Outer edge (darker for definition)\n      gradient.append('stop')\n        .attr('offset', '100%')\n        .attr('stop-color', baseColor?.darker(0.5)?.toString() || d.color)\n        .attr('stop-opacity', '0.25');\n    });\n\n    // Main transparent bubble with inner glow\n    bubbles.append('circle')\n      .attr('r', 0)\n      .attr('fill', (d, i) => `url(#gradient-${i})`)\n      .attr('stroke', (d) => {\n        const baseColor = d3.color(d.color);\n        return baseColor?.brighter(0.8)?.toString() || d.color;\n      })\n      .attr('stroke-width', 2)\n      .attr('stroke-opacity', 0.4)\n      .attr('opacity', 0.9)\n      .attr('filter', 'url(#inner-glow)')\n      .transition()\n      .duration(1000)\n      .delay((d, i) => i * 50)\n      .attr('r', d => d.r)\n      .on('end', function() {\n        if (this.parentNode) {\n          d3.select(this.parentNode as Element).style('opacity', 1);\n        }\n      });\n\n    // Enhanced hover effects with proper transform handling\n    bubbles.on('mouseover', function(event, d) {\n      const circle = d3.select(this).select('circle');\n\n      // Cancel any ongoing transitions to prevent conflicts\n      circle.interrupt();\n\n      // Store the current scale for this bubble\n      d.hoverScale = 1.1;\n\n      // Enhance transparency and glow on hover\n      circle.transition()\n        .duration(200)\n        .attr('opacity', 1)\n        .attr('stroke-width', 3)\n        .attr('stroke', '#fbbf24')\n        .attr('stroke-opacity', 0.8);\n\n      // Bring to front by moving to end of parent (proper SVG z-ordering)\n      const parent = this.parentNode;\n      if (parent) {\n        parent.appendChild(this);\n      }\n\n      setHoveredBubble(d);\n      onBubbleHover?.(d);\n    })\n    .on('mouseout', function(event, d) {\n      const circle = d3.select(this).select('circle');\n\n      // Cancel any ongoing transitions to prevent conflicts\n      circle.interrupt();\n\n      // Reset the scale for this bubble\n      d.hoverScale = 1;\n\n      // Reset to transparent state\n      circle.transition()\n        .duration(200)\n        .attr('opacity', 0.9)\n        .attr('stroke-width', 2)\n        .attr('stroke', function() {\n          const baseColor = d3.color(d.color);\n          return baseColor?.brighter(0.8)?.toString() || d.color;\n        })\n        .attr('stroke-opacity', 0.4);\n\n      setHoveredBubble(null);\n      onBubbleHover?.(null);\n    })\n    .on('click', function(event, d) {\n      // Click animation with proper transform handling\n      // Store original scale\n      const originalScale = d.hoverScale || 1;\n\n      // Animate click effect\n      d.clickScale = 0.95;\n      setTimeout(() => {\n        d.clickScale = originalScale * 1.1;\n        setTimeout(() => {\n          d.clickScale = originalScale;\n        }, 150);\n      }, 150);\n\n      onBubbleClick?.(d);\n    });\n\n    // Add symbol text - show for all bubbles with adaptive sizing\n    bubbles.append('text')\n      .attr('text-anchor', 'middle')\n      .attr('dy', '-0.1em')\n      .attr('font-size', d => Math.max(8, Math.min(d.r * 0.4, 20)))\n      .attr('font-weight', 'bold')\n      .attr('fill', '#ffffff')\n      .attr('pointer-events', 'none')\n      .style('text-shadow', '2px 2px 4px rgba(0,0,0,0.9)')\n      .style('font-family', 'system-ui, -apple-system, sans-serif')\n      .style('dominant-baseline', 'central')\n      .text(d => d.symbol);\n\n    // Add percentage text - show for all bubbles\n    bubbles.append('text')\n      .attr('text-anchor', 'middle')\n      .attr('dy', '0.8em')\n      .attr('font-size', d => Math.max(6, Math.min(d.r * 0.3, 16)))\n      .attr('font-weight', '600')\n      .attr('fill', '#ffffff')\n      .attr('pointer-events', 'none')\n      .style('text-shadow', '2px 2px 4px rgba(0,0,0,0.9)')\n      .style('font-family', 'system-ui, -apple-system, sans-serif')\n      .style('dominant-baseline', 'central')\n      .text(d => `${d.change24h >= 0 ? '+' : ''}${d.change24h.toFixed(2)}%`);\n\n    // Run simulation for enough iterations to settle bubbles without overlap\n    for (let i = 0; i < 500; i++) {\n      simulation.tick();\n    }\n\n    // Update positions on simulation tick with proper scaling\n    simulation.on('tick', () => {\n      bubbles.attr('transform', d => {\n        const scale = d.clickScale || d.hoverScale || 1;\n        return `translate(${d.x},${d.y}) scale(${scale})`;\n      });\n    });\n\n    // Remove zoom functionality - bubbles should fill the screen\n\n    // Cleanup\n    return () => {\n      simulation.stop();\n    };\n  }, [data, width, height, onBubbleClick, onBubbleHover]);\n\n  return (\n    <div className=\"relative\">\n      <svg\n        ref={svgRef}\n        width={width}\n        height={height}\n        className=\"bg-gradient-to-br from-gray-900 to-gray-800\"\n        style={{ display: 'block' }}\n      />\n      \n      {/* Enhanced Tooltip */}\n      {hoveredBubble && (\n        <div className=\"absolute top-4 right-4 bg-black/20 p-5 rounded-xl shadow-2xl border-2 border-white/30 z-20 min-w-72 max-w-80 backdrop-blur-md\" style={{ boxShadow: 'inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)' }}>\n          <div className=\"flex items-center gap-3 mb-3\">\n            <div className=\"w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm\"\n                 style={{ backgroundColor: hoveredBubble.change24h >= 0 ? '#22c55e' : '#ef4444' }}>\n              {hoveredBubble.symbol.charAt(0)}\n            </div>\n            <div>\n              <h3 className=\"font-bold text-lg text-white\">{hoveredBubble.name}</h3>\n              <span className=\"text-white/80 text-sm font-medium\">({hoveredBubble.symbol}) • Rank #{hoveredBubble.rank}</span>\n            </div>\n          </div>\n\n          <div className=\"space-y-2 text-sm\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-white/90 font-medium\">Price:</span>\n              <span className=\"font-bold text-lg text-white\">\n                ${hoveredBubble.price < 1 ? hoveredBubble.price.toFixed(4) : hoveredBubble.price.toLocaleString()}\n              </span>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-white/90 font-medium\">Market Cap:</span>\n              <span className=\"font-semibold text-white\">\n                {hoveredBubble.marketCap >= 1e9\n                  ? `$${(hoveredBubble.marketCap / 1e9).toFixed(2)}B`\n                  : `$${(hoveredBubble.marketCap / 1e6).toFixed(2)}M`\n                }\n              </span>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-white/90 font-medium\">24h Volume:</span>\n              <span className=\"font-semibold text-white\">\n                {hoveredBubble.volume24h >= 1e9\n                  ? `$${(hoveredBubble.volume24h / 1e9).toFixed(2)}B`\n                  : `$${(hoveredBubble.volume24h / 1e6).toFixed(2)}M`\n                }\n              </span>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-white/90 font-medium\">24h Change:</span>\n              <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${\n                hoveredBubble.change24h >= 0\n                  ? 'bg-green-500/20 text-green-300 border border-green-400/30'\n                  : 'bg-red-500/20 text-red-300 border border-red-400/30'\n              }`}>\n                <span>{hoveredBubble.change24h >= 0 ? '↗' : '↘'}</span>\n                <span>{hoveredBubble.change24h >= 0 ? '+' : ''}{hoveredBubble.change24h.toFixed(2)}%</span>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-white/90 font-medium\">7d Change:</span>\n              <span className={`font-semibold ${hoveredBubble.change7d >= 0 ? 'text-green-300' : 'text-red-300'}`}>\n                {hoveredBubble.change7d >= 0 ? '+' : ''}{hoveredBubble.change7d.toFixed(2)}%\n              </span>\n            </div>\n\n            <div className=\"pt-2 border-t border-white/30\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-white/90 font-medium\">Category:</span>\n                <span className=\"px-2 py-1 bg-blue-500/20 text-blue-300 border border-blue-400/30 rounded-full text-xs font-bold\">\n                  {hoveredBubble.category}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-3 pt-3 border-t border-white/30 text-xs text-white/80 text-center font-medium\">\n            Click bubble for detailed view\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAcO,MAAM,cAA0C,CAAC,EACtD,IAAI,EACJ,QAAQ,IAAI,EACZ,SAAS,GAAG,EACZ,aAAa,EACb,aAAa,EACd;IACC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAE1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE;QAErC,MAAM,MAAM,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,OAAO,OAAO;QACpC,IAAI,SAAS,CAAC,KAAK,MAAM;QAEzB,gBAAgB;QAChB,MAAM,eAAe,CAAA,GAAA,2KAAA,CAAA,MAAM,AAAD,EAAE,MAAM,CAAA,IAAK,EAAE,SAAS,KAAK;QACvD,MAAM,eAAe,CAAA,GAAA,2KAAA,CAAA,MAAM,AAAD,EAAE,MAAM,CAAA,IAAK,EAAE,SAAS,KAAK;QAEvD,oEAAoE;QACpE,MAAM,YAAY,QAAQ;QAC1B,MAAM,cAAc,KAAK,MAAM;QAC/B,MAAM,cAAc,YAAY,cAAc,KAAK,iDAAiD;QACpG,MAAM,gBAAgB,KAAK,IAAI,CAAC,cAAc,KAAK,EAAE;QAErD,wDAAwD;QACxD,MAAM,cAAc,CAAA,GAAA,8KAAA,CAAA,YAAY,AAAD,IAC5B,MAAM,CAAC;YAAC;YAAc;SAAa,EACnC,KAAK,CAAC;YAAC,gBAAgB;YAAK,gBAAgB;SAAI,GAAG,2BAA2B;QAEjF,yDAAyD;QACzD,MAAM,aAAa,CAAA,GAAA,sLAAA,CAAA,cAAc,AAAD,IAC7B,MAAM,CAAC;YAAC,CAAC;YAAI,CAAC;YAAG;YAAG;YAAG;SAAG,EAC1B,KAAK,CAAC;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU,EAC7D,KAAK,CAAC;QAET,sBAAsB;QACtB,MAAM,aAA2B,KAAK,GAAG,CAAC,CAAA,SAAU,CAAC;gBACnD,GAAG,MAAM;gBACT,GAAG;gBACH,GAAG;gBACH,GAAG,YAAY,OAAO,SAAS;gBAC/B,OAAO,WAAW,OAAO,SAAS;YACpC,CAAC;QAED,yEAAyE;QACzE,MAAM,aAAa,CAAA,GAAA,8LAAA,CAAA,kBAAkB,AAAD,EAAc,YAC/C,KAAK,CAAC,UAAU,CAAA,GAAA,0LAAA,CAAA,gBAAgB,AAAD,IAAI,QAAQ,CAAC,CAAC,KAAK,wCAAwC;SAC1F,KAAK,CAAC,UAAU,CAAA,GAAA,sLAAA,CAAA,cAAc,AAAD,EAAE,QAAQ,GAAG,SAAS,IACnD,KAAK,CAAC,aAAa,CAAA,GAAA,wLAAA,CAAA,eAAe,AAAD,IAC/B,MAAM,CAAC,CAAA,IAAK,EAAE,CAAC,GAAG,GAAG,mCAAmC;SACxD,QAAQ,CAAC,GAAG,6BAA6B;SACzC,UAAU,CAAC,IAAI,sDAAsD;SACvE,KAAK,CAAC,KAAK,CAAA,GAAA,4KAAA,CAAA,SAAS,AAAD,EAAc,QAAQ,GAAG,QAAQ,CAAC,OAAO,mBAAmB;SAC/E,KAAK,CAAC,KAAK,CAAA,GAAA,4KAAA,CAAA,SAAS,AAAD,EAAc,SAAS,GAAG,QAAQ,CAAC,MACvD,gDAAgD;SAC/C,KAAK,CAAC,YAAY;YACjB,WAAW,OAAO,CAAC,CAAA;gBACjB,MAAM,UAAU,EAAE,CAAC,GAAG;gBACtB,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,QAAQ,SAAS,EAAE,CAAC,IAAI,QAAQ;gBACjE,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,SAAS,EAAE,CAAC,IAAI,SAAS;YACrE;QACF,GACC,UAAU,CAAC,OAAO,wCAAwC;SAC1D,aAAa,CAAC,MAAM,gCAAgC;QAEvD,yBAAyB;QACzB,MAAM,YAAY,IAAI,MAAM,CAAC;QAE7B,yCAAyC;QACzC,MAAM,UAAU,UAAU,SAAS,CAAC,WACjC,IAAI,CAAC,YACL,KAAK,GACL,MAAM,CAAC,KACP,IAAI,CAAC,SAAS,UACd,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,WAAW;QAEpB,mDAAmD;QACnD,MAAM,OAAO,IAAI,MAAM,CAAC;QAExB,2BAA2B;QAC3B,MAAM,aAAa,KAAK,MAAM,CAAC,UAC5B,IAAI,CAAC,MAAM,cACX,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,SAAS,QACd,IAAI,CAAC,UAAU;QAElB,+BAA+B;QAC/B,WAAW,MAAM,CAAC,kBACf,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,UAAU;QAElB,MAAM,UAAU,WAAW,MAAM,CAAC;QAClC,QAAQ,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM;QACzC,QAAQ,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM;QAEzC,4BAA4B;QAC5B,MAAM,eAAe,KAAK,MAAM,CAAC,UAC9B,IAAI,CAAC,MAAM,eACX,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,SAAS,QACd,IAAI,CAAC,UAAU;QAElB,aAAa,MAAM,CAAC,gBACjB,IAAI,CAAC,MAAM,KACX,IAAI,CAAC,MAAM,KACX,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,eAAe;QAEvB,+DAA+D;QAC/D,QAAQ,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACxB,MAAM,WAAW,KAAK,MAAM,CAAC,kBAC1B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,EAC1B,IAAI,CAAC,MAAM,OACX,IAAI,CAAC,MAAM;YAEd,mDAAmD;YACnD,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;YAElC,6BAA6B;YAC7B,SAAS,MAAM,CAAC,QACb,IAAI,CAAC,UAAU,MACf,IAAI,CAAC,cAAc,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK,EAClE,IAAI,CAAC,gBAAgB;YAExB,2BAA2B;YAC3B,SAAS,MAAM,CAAC,QACb,IAAI,CAAC,UAAU,OACf,IAAI,CAAC,cAAc,EAAE,KAAK,EAC1B,IAAI,CAAC,gBAAgB;YAExB,qCAAqC;YACrC,SAAS,MAAM,CAAC,QACb,IAAI,CAAC,UAAU,QACf,IAAI,CAAC,cAAc,WAAW,OAAO,MAAM,cAAc,EAAE,KAAK,EAChE,IAAI,CAAC,gBAAgB;QAC1B;QAEA,0CAA0C;QAC1C,QAAQ,MAAM,CAAC,UACZ,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAM,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,EAC5C,IAAI,CAAC,UAAU,CAAC;YACf,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;YAClC,OAAO,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK;QACxD,GACC,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,kBAAkB,KACvB,IAAI,CAAC,WAAW,KAChB,IAAI,CAAC,UAAU,oBACf,UAAU,GACV,QAAQ,CAAC,MACT,KAAK,CAAC,CAAC,GAAG,IAAM,IAAI,IACpB,IAAI,CAAC,KAAK,CAAA,IAAK,EAAE,CAAC,EAClB,EAAE,CAAC,OAAO;YACT,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,CAAC,UAAU,EAAa,KAAK,CAAC,WAAW;YACzD;QACF;QAEF,wDAAwD;QACxD,QAAQ,EAAE,CAAC,aAAa,SAAS,KAAK,EAAE,CAAC;YACvC,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE,MAAM,CAAC;YAEtC,sDAAsD;YACtD,OAAO,SAAS;YAEhB,0CAA0C;YAC1C,EAAE,UAAU,GAAG;YAEf,yCAAyC;YACzC,OAAO,UAAU,GACd,QAAQ,CAAC,KACT,IAAI,CAAC,WAAW,GAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,UAAU,WACf,IAAI,CAAC,kBAAkB;YAE1B,oEAAoE;YACpE,MAAM,SAAS,IAAI,CAAC,UAAU;YAC9B,IAAI,QAAQ;gBACV,OAAO,WAAW,CAAC,IAAI;YACzB;YAEA,iBAAiB;YACjB,gBAAgB;QAClB,GACC,EAAE,CAAC,YAAY,SAAS,KAAK,EAAE,CAAC;YAC/B,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE,MAAM,CAAC;YAEtC,sDAAsD;YACtD,OAAO,SAAS;YAEhB,kCAAkC;YAClC,EAAE,UAAU,GAAG;YAEf,6BAA6B;YAC7B,OAAO,UAAU,GACd,QAAQ,CAAC,KACT,IAAI,CAAC,WAAW,KAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,UAAU;gBACd,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;gBAClC,OAAO,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK;YACxD,GACC,IAAI,CAAC,kBAAkB;YAE1B,iBAAiB;YACjB,gBAAgB;QAClB,GACC,EAAE,CAAC,SAAS,SAAS,KAAK,EAAE,CAAC;YAC5B,iDAAiD;YACjD,uBAAuB;YACvB,MAAM,gBAAgB,EAAE,UAAU,IAAI;YAEtC,uBAAuB;YACvB,EAAE,UAAU,GAAG;YACf,WAAW;gBACT,EAAE,UAAU,GAAG,gBAAgB;gBAC/B,WAAW;oBACT,EAAE,UAAU,GAAG;gBACjB,GAAG;YACL,GAAG;YAEH,gBAAgB;QAClB;QAEA,8DAA8D;QAC9D,QAAQ,MAAM,CAAC,QACZ,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,UACX,IAAI,CAAC,aAAa,CAAA,IAAK,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,MACvD,IAAI,CAAC,eAAe,QACpB,IAAI,CAAC,QAAQ,WACb,IAAI,CAAC,kBAAkB,QACvB,KAAK,CAAC,eAAe,+BACrB,KAAK,CAAC,eAAe,wCACrB,KAAK,CAAC,qBAAqB,WAC3B,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM;QAErB,6CAA6C;QAC7C,QAAQ,MAAM,CAAC,QACZ,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,SACX,IAAI,CAAC,aAAa,CAAA,IAAK,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,MACvD,IAAI,CAAC,eAAe,OACpB,IAAI,CAAC,QAAQ,WACb,IAAI,CAAC,kBAAkB,QACvB,KAAK,CAAC,eAAe,+BACrB,KAAK,CAAC,eAAe,wCACrB,KAAK,CAAC,qBAAqB,WAC3B,IAAI,CAAC,CAAA,IAAK,GAAG,EAAE,SAAS,IAAI,IAAI,MAAM,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEvE,yEAAyE;QACzE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,WAAW,IAAI;QACjB;QAEA,0DAA0D;QAC1D,WAAW,EAAE,CAAC,QAAQ;YACpB,QAAQ,IAAI,CAAC,aAAa,CAAA;gBACxB,MAAM,QAAQ,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;gBAC9C,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACnD;QACF;QAEA,6DAA6D;QAE7D,UAAU;QACV,OAAO;YACL,WAAW,IAAI;QACjB;IACF,GAAG;QAAC;QAAM;QAAO;QAAQ;QAAe;KAAc;IAEtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAU;gBACV,OAAO;oBAAE,SAAS;gBAAQ;;;;;;YAI3B,+BACC,8OAAC;gBAAI,WAAU;gBAAgI,OAAO;oBAAE,WAAW;gBAAyE;;kCAC1O,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCACV,OAAO;oCAAE,iBAAiB,cAAc,SAAS,IAAI,IAAI,YAAY;gCAAU;0CACjF,cAAc,MAAM,CAAC,MAAM,CAAC;;;;;;0CAE/B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgC,cAAc,IAAI;;;;;;kDAChE,8OAAC;wCAAK,WAAU;;4CAAoC;4CAAE,cAAc,MAAM;4CAAC;4CAAW,cAAc,IAAI;;;;;;;;;;;;;;;;;;;kCAI5G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,8OAAC;wCAAK,WAAU;;4CAA+B;4CAC3C,cAAc,KAAK,GAAG,IAAI,cAAc,KAAK,CAAC,OAAO,CAAC,KAAK,cAAc,KAAK,CAAC,cAAc;;;;;;;;;;;;;0CAInG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,8OAAC;wCAAK,WAAU;kDACb,cAAc,SAAS,IAAI,MACxB,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACjD,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;0CAKzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,8OAAC;wCAAK,WAAU;kDACb,cAAc,SAAS,IAAI,MACxB,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACjD,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;0CAKzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,8OAAC;wCAAI,WAAW,CAAC,iEAAiE,EAChF,cAAc,SAAS,IAAI,IACvB,8DACA,uDACJ;;0DACA,8OAAC;0DAAM,cAAc,SAAS,IAAI,IAAI,MAAM;;;;;;0DAC5C,8OAAC;;oDAAM,cAAc,SAAS,IAAI,IAAI,MAAM;oDAAI,cAAc,SAAS,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAIvF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,8OAAC;wCAAK,WAAW,CAAC,cAAc,EAAE,cAAc,QAAQ,IAAI,IAAI,mBAAmB,gBAAgB;;4CAChG,cAAc,QAAQ,IAAI,IAAI,MAAM;4CAAI,cAAc,QAAQ,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAI/E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,8OAAC;4CAAK,WAAU;sDACb,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;kCAAmF;;;;;;;;;;;;;;;;;;AAO5G", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  text?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'md', \n  text = 'Loading...' \n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <div className={`${sizeClasses[size]} animate-spin rounded-full border-4 border-gray-300 border-t-blue-600`}></div>\n      {text && <p className=\"mt-4 text-gray-600 text-sm\">{text}</p>}\n    </div>\n  );\n};\n\nexport const BubbleChartSkeleton: React.FC = () => {\n  return (\n    <div className=\"w-full h-[700px] bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg border border-gray-200 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"w-16 h-16 animate-spin rounded-full border-4 border-gray-600 border-t-blue-400 mx-auto mb-4\"></div>\n        <p className=\"text-white text-lg font-medium\">Loading cryptocurrency data...</p>\n        <p className=\"text-gray-400 text-sm mt-2\">Preparing interactive bubble chart</p>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,IAAI,EACX,OAAO,YAAY,EACpB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;YAC1G,sBAAQ,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAG1D;AAEO,MAAM,sBAAgC;IAC3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAiC;;;;;;8BAC9C,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/ResponsiveBubbleChart.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Bubble<PERSON>hart } from './BubbleChart';\nimport { BubbleChartSkeleton } from './LoadingSpinner';\nimport { CryptoCurrency } from '@/types';\n\ninterface ResponsiveBubbleChartProps {\n  data: CryptoCurrency[];\n  onBubbleClick?: (crypto: CryptoCurrency) => void;\n  onBubbleHover?: (crypto: CryptoCurrency | null) => void;\n  isLoading?: boolean;\n}\n\nexport const ResponsiveBubbleChart: React.FC<ResponsiveBubbleChartProps> = ({\n  data,\n  onBubbleClick,\n  onBubbleHover,\n  isLoading = false\n}) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const [dimensions, setDimensions] = useState({\n    width: typeof window !== 'undefined' ? window.innerWidth : 1920,\n    height: typeof window !== 'undefined' ? window.innerHeight : 1080\n  });\n\n  useEffect(() => {\n    const updateDimensions = () => {\n      // Use full viewport dimensions\n      setDimensions({\n        width: window.innerWidth,\n        height: window.innerHeight\n      });\n    };\n\n    updateDimensions();\n    window.addEventListener('resize', updateDimensions);\n\n    return () => window.removeEventListener('resize', updateDimensions);\n  }, []);\n\n  if (isLoading) {\n    return <BubbleChartSkeleton />;\n  }\n\n  return (\n    <div ref={containerRef} className=\"fixed inset-0 w-full h-full\">\n      <BubbleChart\n        data={data}\n        width={dimensions.width}\n        height={dimensions.height}\n        onBubbleClick={onBubbleClick}\n        onBubbleHover={onBubbleHover}\n      />\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcO,MAAM,wBAA8D,CAAC,EAC1E,IAAI,EACJ,aAAa,EACb,aAAa,EACb,YAAY,KAAK,EAClB;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO,6EAAoD;QAC3D,QAAQ,6EAAqD;IAC/D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,+BAA+B;YAC/B,cAAc;gBACZ,OAAO,OAAO,UAAU;gBACxB,QAAQ,OAAO,WAAW;YAC5B;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBAAO,8OAAC,oIAAA,CAAA,sBAAmB;;;;;IAC7B;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAU;kBAChC,cAAA,8OAAC,iIAAA,CAAA,cAAW;YACV,MAAM;YACN,OAAO,WAAW,KAAK;YACvB,QAAQ,WAAW,MAAM;YACzB,eAAe;YACf,eAAe;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/SearchAndFilter.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Search, Filter, TrendingUp, TrendingDown } from 'lucide-react';\nimport { FilterOptions } from '@/types';\n\ninterface SearchAndFilterProps {\n  filters: FilterOptions;\n  onFiltersChange: (filters: FilterOptions) => void;\n  categories: string[];\n  compact?: boolean;\n}\n\nexport const SearchAndFilter: React.FC<SearchAndFilterProps> = ({\n  filters,\n  onFiltersChange,\n  categories,\n  compact = false\n}) => {\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onFiltersChange({ ...filters, searchTerm: e.target.value });\n  };\n\n  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    onFiltersChange({ \n      ...filters, \n      category: e.target.value === 'all' ? undefined : e.target.value \n    });\n  };\n\n  const handleSortChange = (sortBy: FilterOptions['sortBy']) => {\n    const newSortOrder = filters.sortBy === sortBy && filters.sortOrder === 'desc' ? 'asc' : 'desc';\n    onFiltersChange({ ...filters, sortBy, sortOrder: newSortOrder });\n  };\n\n  if (compact) {\n    return (\n      <div className=\"space-y-3\">\n        {/* Compact Search */}\n        <div className=\"relative\">\n          <Search className=\"absolute left-2 top-1/2 transform -translate-y-1/2 text-white/70 w-3 h-3\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search...\"\n            value={filters.searchTerm || ''}\n            onChange={handleSearchChange}\n            className=\"w-full pl-7 pr-3 py-1.5 text-sm text-white font-medium bg-white/10 border border-white/30 rounded focus:ring-1 focus:ring-blue-400 focus:border-blue-400 placeholder-white/60 backdrop-blur-sm\"\n          />\n        </div>\n\n        {/* Compact Category Filter */}\n        <select\n          value={filters.category || 'all'}\n          onChange={handleCategoryChange}\n          className=\"w-full px-2 py-1.5 text-sm text-white font-medium bg-white/10 border border-white/30 rounded focus:ring-1 focus:ring-blue-400 focus:border-blue-400 backdrop-blur-sm\"\n        >\n          <option value=\"all\">All Categories</option>\n          {categories.map(category => (\n            <option key={category} value={category}>\n              {category}\n            </option>\n          ))}\n        </select>\n\n        {/* Compact Sort Options */}\n        <div className=\"flex gap-1\">\n          <button\n            onClick={() => handleSortChange('marketCap')}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === 'marketCap'\n                ? 'bg-blue-500 text-white border-blue-500'\n                : 'bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm'\n            }`}\n          >\n            Cap\n          </button>\n\n          <button\n            onClick={() => handleSortChange('change24h')}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === 'change24h'\n                ? 'bg-blue-500 text-white border-blue-500'\n                : 'bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm'\n            }`}\n          >\n            24h\n          </button>\n\n          <button\n            onClick={() => handleSortChange('price')}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === 'price'\n                ? 'bg-blue-500 text-white border-blue-500'\n                : 'bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm'\n            }`}\n          >\n            Price\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow-lg border mb-6\">\n      <div className=\"flex flex-col lg:flex-row gap-4 items-center\">\n        {/* Search */}\n        <div className=\"relative flex-1 min-w-64\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 w-4 h-4\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search cryptocurrencies...\"\n            value={filters.searchTerm || ''}\n            onChange={handleSearchChange}\n            className=\"w-full pl-10 pr-4 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-600\"\n          />\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"flex items-center gap-2\">\n          <Filter className=\"text-gray-600 w-4 h-4\" />\n          <select\n            value={filters.category || 'all'}\n            onChange={handleCategoryChange}\n            className=\"px-3 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Categories</option>\n            {categories.map(category => (\n              <option key={category} value={category}>\n                {category}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* Sort Options */}\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => handleSortChange('marketCap')}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === 'marketCap'\n                ? 'bg-blue-500 text-white border-blue-500'\n                : 'bg-white text-black border-gray-300 hover:bg-gray-50'\n            }`}\n          >\n            Market Cap\n            {filters.sortBy === 'marketCap' && (\n              filters.sortOrder === 'desc' ? <TrendingDown className=\"inline w-4 h-4 ml-1\" /> : <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n            )}\n          </button>\n\n          <button\n            onClick={() => handleSortChange('change24h')}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === 'change24h'\n                ? 'bg-blue-500 text-white border-blue-500'\n                : 'bg-white text-black border-gray-300 hover:bg-gray-50'\n            }`}\n          >\n            24h Change\n            {filters.sortBy === 'change24h' && (\n              filters.sortOrder === 'desc' ? <TrendingDown className=\"inline w-4 h-4 ml-1\" /> : <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n            )}\n          </button>\n\n          <button\n            onClick={() => handleSortChange('price')}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === 'price'\n                ? 'bg-blue-500 text-white border-blue-500'\n                : 'bg-white text-black border-gray-300 hover:bg-gray-50'\n            }`}\n          >\n            Price\n            {filters.sortBy === 'price' && (\n              filters.sortOrder === 'desc' ? <TrendingDown className=\"inline w-4 h-4 ml-1\" /> : <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* Active Filters Display */}\n      {(filters.searchTerm || filters.category) && (\n        <div className=\"mt-4 flex flex-wrap gap-2\">\n          {filters.searchTerm && (\n            <span className=\"px-3 py-1 bg-blue-100 text-blue-900 rounded-full text-sm font-semibold\">\n              Search: &ldquo;{filters.searchTerm}&rdquo;\n              <button\n                onClick={() => onFiltersChange({ ...filters, searchTerm: undefined })}\n                className=\"ml-2 text-blue-700 hover:text-blue-900 font-bold\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          {filters.category && (\n            <span className=\"px-3 py-1 bg-green-100 text-green-900 rounded-full text-sm font-semibold\">\n              Category: {filters.category}\n              <button\n                onClick={() => onFiltersChange({ ...filters, category: undefined })}\n                className=\"ml-2 text-green-700 hover:text-green-900 font-bold\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAaO,MAAM,kBAAkD,CAAC,EAC9D,OAAO,EACP,eAAe,EACf,UAAU,EACV,UAAU,KAAK,EAChB;IACC,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;YAAE,GAAG,OAAO;YAAE,YAAY,EAAE,MAAM,CAAC,KAAK;QAAC;IAC3D;IAEA,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;YACd,GAAG,OAAO;YACV,UAAU,EAAE,MAAM,CAAC,KAAK,KAAK,QAAQ,YAAY,EAAE,MAAM,CAAC,KAAK;QACjE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,QAAQ,MAAM,KAAK,UAAU,QAAQ,SAAS,KAAK,SAAS,QAAQ;QACzF,gBAAgB;YAAE,GAAG,OAAO;YAAE;YAAQ,WAAW;QAAa;IAChE;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO,QAAQ,UAAU,IAAI;4BAC7B,UAAU;4BACV,WAAU;;;;;;;;;;;;8BAKd,8OAAC;oBACC,OAAO,QAAQ,QAAQ,IAAI;oBAC3B,UAAU;oBACV,WAAU;;sCAEV,8OAAC;4BAAO,OAAM;sCAAM;;;;;;wBACnB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;gCAAsB,OAAO;0CAC3B;+BADU;;;;;;;;;;;8BAOjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,cACf,2CACA,6EACJ;sCACH;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,cACf,2CACA,6EACJ;sCACH;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,UACf,2CACA,6EACJ;sCACH;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,QAAQ,UAAU,IAAI;gCAC7B,UAAU;gCACV,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,OAAO,QAAQ,QAAQ,IAAI;gCAC3B,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;oCACnB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;4CAAsB,OAAO;sDAC3B;2CADU;;;;;;;;;;;;;;;;;kCAQnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,cACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,eAAe,CACjC,QAAQ,SAAS,KAAK,uBAAS,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAA2B,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CAC1G;;;;;;;0CAGF,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,cACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,eAAe,CACjC,QAAQ,SAAS,KAAK,uBAAS,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAA2B,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CAC1G;;;;;;;0CAGF,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,UACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,WAAW,CAC7B,QAAQ,SAAS,KAAK,uBAAS,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAA2B,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CAC1G;;;;;;;;;;;;;;;;;;;YAML,CAAC,QAAQ,UAAU,IAAI,QAAQ,QAAQ,mBACtC,8OAAC;gBAAI,WAAU;;oBACZ,QAAQ,UAAU,kBACjB,8OAAC;wBAAK,WAAU;;4BAAyE;4BACvE,QAAQ,UAAU;4BAAC;0CACnC,8OAAC;gCACC,SAAS,IAAM,gBAAgB;wCAAE,GAAG,OAAO;wCAAE,YAAY;oCAAU;gCACnE,WAAU;0CACX;;;;;;;;;;;;oBAKJ,QAAQ,QAAQ,kBACf,8OAAC;wBAAK,WAAU;;4BAA2E;4BAC9E,QAAQ,QAAQ;0CAC3B,8OAAC;gCACC,SAAS,IAAM,gBAAgB;wCAAE,GAAG,OAAO;wCAAE,UAAU;oCAAU;gCACjE,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/CryptoDetailModal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { X, TrendingUp, TrendingDown, DollarSign, BarChart3, Volume2 } from 'lucide-react';\nimport { CryptoCurrency } from '@/types';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface CryptoDetailModalProps {\n  crypto: CryptoCurrency | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport const CryptoDetailModal: React.FC<CryptoDetailModalProps> = ({\n  crypto,\n  isOpen,\n  onClose\n}) => {\n  if (!crypto) return null;\n\n  const formatCurrency = (value: number) => {\n    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;\n    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;\n    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;\n    return `$${value.toFixed(2)}`;\n  };\n\n  const formatNumber = (value: number) => {\n    return value.toLocaleString();\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 bg-black bg-opacity-50\"\n            onClick={onClose}\n          />\n          \n          {/* Modal */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            className=\"relative bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\"\n          >\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-6 border-b\">\n              <div className=\"flex items-center gap-4\">\n                <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg\">\n                  {crypto.symbol.charAt(0)}\n                </div>\n                <div>\n                  <h2 className=\"text-2xl font-bold text-gray-900\">{crypto.name}</h2>\n                  <p className=\"text-gray-500\">{crypto.symbol} • Rank #{crypto.rank}</p>\n                </div>\n              </div>\n              <button\n                onClick={onClose}\n                className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n              >\n                <X className=\"w-6 h-6 text-gray-500\" />\n              </button>\n            </div>\n\n            {/* Content */}\n            <div className=\"p-6\">\n              {/* Price Section */}\n              <div className=\"mb-8\">\n                <div className=\"flex items-center gap-4 mb-4\">\n                  <span className=\"text-4xl font-bold text-gray-900\">\n                    ${formatNumber(crypto.price)}\n                  </span>\n                  <div className={`flex items-center gap-1 px-3 py-1 rounded-full ${\n                    crypto.change24h >= 0 \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-red-100 text-red-800'\n                  }`}>\n                    {crypto.change24h >= 0 ? (\n                      <TrendingUp className=\"w-4 h-4\" />\n                    ) : (\n                      <TrendingDown className=\"w-4 h-4\" />\n                    )}\n                    <span className=\"font-semibold\">\n                      {crypto.change24h >= 0 ? '+' : ''}{crypto.change24h.toFixed(2)}%\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"text-sm text-gray-600\">\n                  7d change: \n                  <span className={`ml-1 font-semibold ${\n                    crypto.change7d >= 0 ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {crypto.change7d >= 0 ? '+' : ''}{crypto.change7d.toFixed(2)}%\n                  </span>\n                </div>\n              </div>\n\n              {/* Stats Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <BarChart3 className=\"w-5 h-5 text-blue-500\" />\n                    <span className=\"font-semibold text-gray-700\">Market Cap</span>\n                  </div>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {formatCurrency(crypto.marketCap)}\n                  </p>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <Volume2 className=\"w-5 h-5 text-purple-500\" />\n                    <span className=\"font-semibold text-gray-700\">24h Volume</span>\n                  </div>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {formatCurrency(crypto.volume24h)}\n                  </p>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <DollarSign className=\"w-5 h-5 text-green-500\" />\n                    <span className=\"font-semibold text-gray-700\">Category</span>\n                  </div>\n                  <p className=\"text-xl font-bold text-gray-900\">\n                    {crypto.category}\n                  </p>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <TrendingUp className=\"w-5 h-5 text-orange-500\" />\n                    <span className=\"font-semibold text-gray-700\">Volume/MCap</span>\n                  </div>\n                  <p className=\"text-xl font-bold text-gray-900\">\n                    {((crypto.volume24h / crypto.marketCap) * 100).toFixed(2)}%\n                  </p>\n                </div>\n              </div>\n\n              {/* Description */}\n              {crypto.description && (\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">About</h3>\n                  <p className=\"text-gray-600 leading-relaxed\">{crypto.description}</p>\n                </div>\n              )}\n\n              {/* Mock Chart Placeholder */}\n              <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Price Chart (7 days)</h3>\n                <div className=\"h-32 bg-white rounded border-2 border-dashed border-gray-300 flex items-center justify-center\">\n                  <p className=\"text-gray-500\">Chart visualization would go here</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Footer */}\n            <div className=\"p-6 border-t bg-gray-50 rounded-b-xl\">\n              <div className=\"flex justify-between items-center text-sm text-gray-600\">\n                <span>Last updated: Just now</span>\n                <span>Data provided by CryptoBubble</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AALA;;;;AAaO,MAAM,oBAAsD,CAAC,EAClE,MAAM,EACN,MAAM,EACN,OAAO,EACR;IACC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB,CAAC;QACtB,IAAI,SAAS,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,SAAS,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,SAAS,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxD,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;IAC/B;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,MAAM,cAAc;IAC7B;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,OAAO,MAAM,CAAC,MAAM,CAAC;;;;;;sDAExB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC,OAAO,IAAI;;;;;;8DAC7D,8OAAC;oDAAE,WAAU;;wDAAiB,OAAO,MAAM;wDAAC;wDAAU,OAAO,IAAI;;;;;;;;;;;;;;;;;;;8CAGrE,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAmC;wDAC/C,aAAa,OAAO,KAAK;;;;;;;8DAE7B,8OAAC;oDAAI,WAAW,CAAC,+CAA+C,EAC9D,OAAO,SAAS,IAAI,IAChB,gCACA,2BACJ;;wDACC,OAAO,SAAS,IAAI,kBACnB,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;iFAEtB,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEAE1B,8OAAC;4DAAK,WAAU;;gEACb,OAAO,SAAS,IAAI,IAAI,MAAM;gEAAI,OAAO,SAAS,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;sDAKrE,8OAAC;4CAAI,WAAU;;gDAAwB;8DAErC,8OAAC;oDAAK,WAAW,CAAC,mBAAmB,EACnC,OAAO,QAAQ,IAAI,IAAI,mBAAmB,gBAC1C;;wDACC,OAAO,QAAQ,IAAI,IAAI,MAAM;wDAAI,OAAO,QAAQ,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;8CAMnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAE,WAAU;8DACV,eAAe,OAAO,SAAS;;;;;;;;;;;;sDAIpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAE,WAAU;8DACV,eAAe,OAAO,SAAS;;;;;;;;;;;;sDAIpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAE,WAAU;8DACV,OAAO,QAAQ;;;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAE,WAAU;;wDACV,CAAC,AAAC,OAAO,SAAS,GAAG,OAAO,SAAS,GAAI,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;gCAM/D,OAAO,WAAW,kBACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAiC,OAAO,WAAW;;;;;;;;;;;;8CAKpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAMnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 1523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/data/mockCryptoData.ts"], "sourcesContent": ["export interface CryptoCurrency {\n  id: string;\n  name: string;\n  symbol: string;\n  price: number;\n  marketCap: number;\n  volume24h: number;\n  change24h: number;\n  change7d: number;\n  rank: number;\n  logo?: string;\n  description?: string;\n  website?: string;\n  category: string;\n}\n\nexport const mockCryptoData: CryptoCurrency[] = [\n  {\n    id: \"bitcoin\",\n    name: \"Bitcoin\",\n    symbol: \"BTC\",\n    price: 43250.75,\n    marketCap: 847500000000,\n    volume24h: 28500000000,\n    change24h: 2.45,\n    change7d: -1.23,\n    rank: 1,\n    category: \"Layer 1\",\n    description: \"The first and largest cryptocurrency by market cap\"\n  },\n  {\n    id: \"ethereum\",\n    name: \"Ethereum\",\n    symbol: \"ETH\",\n    price: 2650.30,\n    marketCap: 318750000000,\n    volume24h: 15200000000,\n    change24h: 3.78,\n    change7d: 5.42,\n    rank: 2,\n    category: \"Smart Contract Platform\",\n    description: \"Decentralized platform for smart contracts and DApps\"\n  },\n  {\n    id: \"tether\",\n    name: \"Tether\",\n    symbol: \"USDT\",\n    price: 1.00,\n    marketCap: 91800000000,\n    volume24h: 45600000000,\n    change24h: 0.02,\n    change7d: -0.01,\n    rank: 3,\n    category: \"Stablecoin\",\n    description: \"USD-pegged stablecoin\"\n  },\n  {\n    id: \"bnb\",\n    name: \"BNB\",\n    symbol: \"BNB\",\n    price: 315.80,\n    marketCap: 47370000000,\n    volume24h: 1850000000,\n    change24h: -1.25,\n    change7d: 8.90,\n    rank: 4,\n    category: \"Exchange Token\",\n    description: \"Binance ecosystem token\"\n  },\n  {\n    id: \"solana\",\n    name: \"Solana\",\n    symbol: \"SOL\",\n    price: 98.45,\n    marketCap: 43200000000,\n    volume24h: 2100000000,\n    change24h: 5.67,\n    change7d: 12.34,\n    rank: 5,\n    category: \"Layer 1\",\n    description: \"High-performance blockchain for DeFi and Web3\"\n  },\n  {\n    id: \"usdc\",\n    name: \"USD Coin\",\n    symbol: \"USDC\",\n    price: 1.00,\n    marketCap: 32500000000,\n    volume24h: 5800000000,\n    change24h: 0.01,\n    change7d: 0.00,\n    rank: 6,\n    category: \"Stablecoin\",\n    description: \"Regulated USD-backed stablecoin\"\n  },\n  {\n    id: \"xrp\",\n    name: \"XRP\",\n    symbol: \"XRP\",\n    price: 0.62,\n    marketCap: 33800000000,\n    volume24h: 1200000000,\n    change24h: -2.15,\n    change7d: -5.67,\n    rank: 7,\n    category: \"Payment\",\n    description: \"Digital payment protocol for financial institutions\"\n  },\n  {\n    id: \"cardano\",\n    name: \"Cardano\",\n    symbol: \"ADA\",\n    price: 0.48,\n    marketCap: 16900000000,\n    volume24h: 450000000,\n    change24h: 1.89,\n    change7d: 3.45,\n    rank: 8,\n    category: \"Layer 1\",\n    description: \"Proof-of-stake blockchain platform\"\n  },\n  {\n    id: \"avalanche\",\n    name: \"Avalanche\",\n    symbol: \"AVAX\",\n    price: 36.75,\n    marketCap: 14200000000,\n    volume24h: 680000000,\n    change24h: 4.23,\n    change7d: 15.67,\n    rank: 9,\n    category: \"Layer 1\",\n    description: \"Platform for decentralized applications and custom blockchain networks\"\n  },\n  {\n    id: \"dogecoin\",\n    name: \"Dogecoin\",\n    symbol: \"DOGE\",\n    price: 0.085,\n    marketCap: 12100000000,\n    volume24h: 890000000,\n    change24h: -3.45,\n    change7d: 2.10,\n    rank: 10,\n    category: \"Meme\",\n    description: \"The original meme cryptocurrency\"\n  },\n  {\n    id: \"chainlink\",\n    name: \"Chainlink\",\n    symbol: \"LINK\",\n    price: 14.85,\n    marketCap: 8750000000,\n    volume24h: 420000000,\n    change24h: 2.67,\n    change7d: 8.90,\n    rank: 11,\n    category: \"Oracle\",\n    description: \"Decentralized oracle network\"\n  },\n  {\n    id: \"polygon\",\n    name: \"Polygon\",\n    symbol: \"MATIC\",\n    price: 0.89,\n    marketCap: 8200000000,\n    volume24h: 380000000,\n    change24h: 6.78,\n    change7d: 18.45,\n    rank: 12,\n    category: \"Layer 2\",\n    description: \"Ethereum scaling solution\"\n  },\n  {\n    id: \"litecoin\",\n    name: \"Litecoin\",\n    symbol: \"LTC\",\n    price: 72.30,\n    marketCap: 5400000000,\n    volume24h: 320000000,\n    change24h: -1.56,\n    change7d: 4.23,\n    rank: 13,\n    category: \"Payment\",\n    description: \"Peer-to-peer cryptocurrency based on Bitcoin\"\n  },\n  {\n    id: \"uniswap\",\n    name: \"Uniswap\",\n    symbol: \"UNI\",\n    price: 6.45,\n    marketCap: 4850000000,\n    volume24h: 180000000,\n    change24h: 3.89,\n    change7d: 12.67,\n    rank: 14,\n    category: \"DeFi\",\n    description: \"Decentralized exchange protocol\"\n  },\n  {\n    id: \"internet-computer\",\n    name: \"Internet Computer\",\n    symbol: \"ICP\",\n    price: 12.75,\n    marketCap: 5900000000,\n    volume24h: 95000000,\n    change24h: -2.34,\n    change7d: 7.89,\n    rank: 15,\n    category: \"Layer 1\",\n    description: \"Blockchain computer that scales smart contract computation\"\n  }\n];\n\n// Simple seeded random number generator for consistent results\nclass SeededRandom {\n  private seed: number;\n\n  constructor(seed: number) {\n    this.seed = seed;\n  }\n\n  next(): number {\n    this.seed = (this.seed * 9301 + 49297) % 233280;\n    return this.seed / 233280;\n  }\n}\n\n// Helper function to generate additional random crypto data with deterministic values\nexport const generateRandomCrypto = (count: number): CryptoCurrency[] => {\n  const categories = [\"DeFi\", \"Layer 1\", \"Layer 2\", \"Meme\", \"Gaming\", \"NFT\", \"Oracle\", \"Privacy\", \"Storage\"];\n  const cryptoNames = [\n    \"ApeCoin\", \"Shiba Inu\", \"Cosmos\", \"Algorand\", \"VeChain\", \"Filecoin\", \"Sandbox\",\n    \"Decentraland\", \"Axie Infinity\", \"Theta\", \"Hedera\", \"Elrond\", \"Near Protocol\",\n    \"Flow\", \"Tezos\", \"Fantom\", \"Harmony\", \"Zilliqa\", \"Enjin\", \"Basic Attention Token\",\n    \"Compound\", \"Maker\", \"Aave\", \"Curve\", \"SushiSwap\", \"PancakeSwap\", \"1inch\",\n    \"Yearn Finance\", \"Synthetix\", \"Balancer\", \"Bancor\", \"Kyber Network\", \"0x Protocol\"\n  ];\n\n  // Use a fixed seed for consistent results across server and client\n  const rng = new SeededRandom(12345);\n\n  return Array.from({ length: count }, (_, i) => {\n    const basePrice = rng.next() * 1000 + 0.01;\n    const marketCap = basePrice * (rng.next() * 1000000000 + 1000000);\n\n    return {\n      id: `crypto-${i + 16}`,\n      name: cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`,\n      symbol: `C${i + 16}`,\n      price: Number(basePrice.toFixed(4)),\n      marketCap: Number(marketCap.toFixed(0)),\n      volume24h: Number((marketCap * (rng.next() * 0.3 + 0.05)).toFixed(0)),\n      change24h: Number(((rng.next() - 0.5) * 20).toFixed(2)),\n      change7d: Number(((rng.next() - 0.5) * 40).toFixed(2)),\n      rank: i + 16,\n      category: categories[Math.floor(rng.next() * categories.length)],\n      description: `Description for ${cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`}`\n    };\n  });\n};\n\n// Combine static data with generated data for a total of 50+ cryptocurrencies\nexport const getAllCryptoData = (): CryptoCurrency[] => {\n  return [...mockCryptoData, ...generateRandomCrypto(35)];\n};\n"], "names": [], "mappings": ";;;;;AAgBO,MAAM,iBAAmC;IAC9C;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;CACD;AAED,+DAA+D;AAC/D,MAAM;IACI,KAAa;IAErB,YAAY,IAAY,CAAE;QACxB,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,OAAe;QACb,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,KAAK,IAAI;QACzC,OAAO,IAAI,CAAC,IAAI,GAAG;IACrB;AACF;AAGO,MAAM,uBAAuB,CAAC;IACnC,MAAM,aAAa;QAAC;QAAQ;QAAW;QAAW;QAAQ;QAAU;QAAO;QAAU;QAAW;KAAU;IAC1G,MAAM,cAAc;QAClB;QAAW;QAAa;QAAU;QAAY;QAAW;QAAY;QACrE;QAAgB;QAAiB;QAAS;QAAU;QAAU;QAC9D;QAAQ;QAAS;QAAU;QAAW;QAAW;QAAS;QAC1D;QAAY;QAAS;QAAQ;QAAS;QAAa;QAAe;QAClE;QAAiB;QAAa;QAAY;QAAU;QAAiB;KACtE;IAED,mEAAmE;IACnE,MAAM,MAAM,IAAI,aAAa;IAE7B,OAAO,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAM,GAAG,CAAC,GAAG;QACvC,MAAM,YAAY,IAAI,IAAI,KAAK,OAAO;QACtC,MAAM,YAAY,YAAY,CAAC,IAAI,IAAI,KAAK,aAAa,OAAO;QAEhE,OAAO;YACL,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;YACtB,MAAM,WAAW,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;YAC/D,QAAQ,CAAC,CAAC,EAAE,IAAI,IAAI;YACpB,OAAO,OAAO,UAAU,OAAO,CAAC;YAChC,WAAW,OAAO,UAAU,OAAO,CAAC;YACpC,WAAW,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC;YAClE,WAAW,OAAO,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,EAAE,OAAO,CAAC;YACpD,UAAU,OAAO,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,EAAE,OAAO,CAAC;YACnD,MAAM,IAAI;YACV,UAAU,UAAU,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,WAAW,MAAM,EAAE;YAChE,aAAa,CAAC,gBAAgB,EAAE,WAAW,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,EAAE;QAC7F;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,OAAO;WAAI;WAAmB,qBAAqB;KAAI;AACzD", "debugId": null}}, {"offset": {"line": 1817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo, useEffect } from 'react';\nimport { ResponsiveBubbleChart } from '@/components/ResponsiveBubbleChart';\nimport { SearchAndFilter } from '@/components/SearchAndFilter';\nimport { CryptoDetailModal } from '@/components/CryptoDetailModal';\nimport { getAllCryptoData } from '@/data/mockCryptoData';\nimport { CryptoCurrency, FilterOptions } from '@/types';\n\nexport default function Home() {\n  const [selectedCrypto, setSelectedCrypto] = useState<CryptoCurrency | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [filters, setFilters] = useState<FilterOptions>({\n    sortBy: 'marketCap',\n    sortOrder: 'desc'\n  });\n\n  const allCryptoData = getAllCryptoData();\n\n  // Simulate loading for demo purposes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 1500);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // Get unique categories for filter dropdown\n  const categories = useMemo(() => {\n    const uniqueCategories = [...new Set(allCryptoData.map(crypto => crypto.category))];\n    return uniqueCategories.sort();\n  }, [allCryptoData]);\n\n  // Filter and sort data\n  const filteredData = useMemo(() => {\n    let filtered = allCryptoData;\n\n    // Apply search filter\n    if (filters.searchTerm) {\n      const searchLower = filters.searchTerm.toLowerCase();\n      filtered = filtered.filter(crypto =>\n        crypto.name.toLowerCase().includes(searchLower) ||\n        crypto.symbol.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply category filter\n    if (filters.category) {\n      filtered = filtered.filter(crypto => crypto.category === filters.category);\n    }\n\n    // Apply sorting\n    if (filters.sortBy) {\n      filtered.sort((a, b) => {\n        const aValue = a[filters.sortBy!];\n        const bValue = b[filters.sortBy!];\n\n        if (typeof aValue === 'string' && typeof bValue === 'string') {\n          return filters.sortOrder === 'desc'\n            ? bValue.localeCompare(aValue)\n            : aValue.localeCompare(bValue);\n        }\n\n        return filters.sortOrder === 'desc'\n          ? (bValue as number) - (aValue as number)\n          : (aValue as number) - (bValue as number);\n      });\n    }\n\n    return filtered;\n  }, [allCryptoData, filters]);\n\n  const handleBubbleClick = (crypto: CryptoCurrency) => {\n    setSelectedCrypto(crypto);\n    setIsModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedCrypto(null);\n  };\n\n  return (\n    <div className=\"w-full h-screen overflow-hidden\">\n      {/* Full Screen Bubble Chart */}\n      <ResponsiveBubbleChart\n        data={filteredData}\n        onBubbleClick={handleBubbleClick}\n        isLoading={isLoading}\n      />\n\n      {/* Floating Search and Filter Controls */}\n      <div className=\"fixed top-4 left-4 z-50\">\n        <div className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30\" style={{ boxShadow: 'inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)' }}>\n          <SearchAndFilter\n            filters={filters}\n            onFiltersChange={setFilters}\n            categories={categories}\n            compact={true}\n          />\n        </div>\n      </div>\n\n      {/* Floating Stats Panel */}\n      <div className=\"fixed bottom-4 right-4 z-50\">\n        <div className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30 min-w-64\" style={{ boxShadow: 'inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)' }}>\n          <h3 className=\"font-semibold text-white mb-3\">Market Stats</h3>\n          <div className=\"grid grid-cols-2 gap-3 text-sm\">\n            <div className=\"text-center p-2 bg-green-50 rounded\">\n              <div className=\"text-lg font-bold text-green-700\">\n                {filteredData.filter(c => c.change24h > 0).length}\n              </div>\n              <div className=\"text-xs text-white font-medium\">Gainers</div>\n            </div>\n            <div className=\"text-center p-2 bg-red-50 rounded\">\n              <div className=\"text-lg font-bold text-red-700\">\n                {filteredData.filter(c => c.change24h < 0).length}\n              </div>\n              <div className=\"text-xs text-white font-medium\">Losers</div>\n            </div>\n          </div>\n          <div className=\"mt-3 text-xs text-white font-medium\">\n            <div>Total: {filteredData.length} cryptocurrencies</div>\n            <div>Market Cap: ${(filteredData.reduce((sum, c) => sum + c.marketCap, 0) / 1e12).toFixed(2)}T</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Legend */}\n      <div className=\"fixed bottom-4 left-4 z-50\">\n        <div className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30\" style={{ boxShadow: 'inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)' }}>\n          <h3 className=\"font-semibold text-white mb-3 text-sm\">Legend</h3>\n          <div className=\"space-y-2 text-xs\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n              <span className=\"text-white\">Price increase (24h)</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n              <span className=\"text-white\">Price decrease (24h)</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-4 h-4 bg-gray-400 rounded-full\"></div>\n              <span className=\"text-white\">Bubble size = Market cap</span>\n            </div>\n            <div className=\"pt-2 border-t border-white/30 text-xs text-white\">\n              <div>• Hover bubbles for details</div>\n              <div>• Click bubbles for more info</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Detail Modal */}\n      <CryptoDetailModal\n        crypto={selectedCrypto}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,WAAW;IACb;IAEA,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;IAErC,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,4CAA4C;IAC5C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,mBAAmB;eAAI,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,SAAU,OAAO,QAAQ;SAAG;QACnF,OAAO,iBAAiB,IAAI;IAC9B,GAAG;QAAC;KAAc;IAElB,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,WAAW;QAEf,sBAAsB;QACtB,IAAI,QAAQ,UAAU,EAAE;YACtB,MAAM,cAAc,QAAQ,UAAU,CAAC,WAAW;YAClD,WAAW,SAAS,MAAM,CAAC,CAAA,SACzB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACnC,OAAO,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC;QAEzC;QAEA,wBAAwB;QACxB,IAAI,QAAQ,QAAQ,EAAE;YACpB,WAAW,SAAS,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK,QAAQ,QAAQ;QAC3E;QAEA,gBAAgB;QAChB,IAAI,QAAQ,MAAM,EAAE;YAClB,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,MAAM,SAAS,CAAC,CAAC,QAAQ,MAAM,CAAE;gBACjC,MAAM,SAAS,CAAC,CAAC,QAAQ,MAAM,CAAE;gBAEjC,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;oBAC5D,OAAO,QAAQ,SAAS,KAAK,SACzB,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;gBAC3B;gBAEA,OAAO,QAAQ,SAAS,KAAK,SACzB,AAAC,SAAqB,SACtB,AAAC,SAAqB;YAC5B;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAe;KAAQ;IAE3B,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,2IAAA,CAAA,wBAAqB;gBACpB,MAAM;gBACN,eAAe;gBACf,WAAW;;;;;;0BAIb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgF,OAAO;wBAAE,WAAW;oBAAyE;8BAC1L,cAAA,8OAAC,qIAAA,CAAA,kBAAe;wBACd,SAAS;wBACT,iBAAiB;wBACjB,YAAY;wBACZ,SAAS;;;;;;;;;;;;;;;;0BAMf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAyF,OAAO;wBAAE,WAAW;oBAAyE;;sCACnM,8OAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,GAAG,MAAM;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;;;;;;;8CAElD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,GAAG,MAAM;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;sCAGpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAI;wCAAQ,aAAa,MAAM;wCAAC;;;;;;;8CACjC,8OAAC;;wCAAI;wCAAc,CAAC,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,EAAE,KAAK,IAAI,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAMnG,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgF,OAAO;wBAAE,WAAW;oBAAyE;;sCAC1L,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAI;;;;;;sDACL,8OAAC;sDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOb,8OAAC,uIAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}