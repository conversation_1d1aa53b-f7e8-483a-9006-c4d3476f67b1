export interface CryptoCurrency {
  id: string;
  name: string;
  symbol: string;
  price: number;
  marketCap: number;
  volume24h: number;
  change24h: number;
  change7d: number;
  rank: number;
  logo?: string;
  description?: string;
  website?: string;
  category: string;
}

export interface BubbleData extends CryptoCurrency {
  x: number;
  y: number;
  r: number;
  color: string;
  hoverScale?: number;
  clickScale?: number;
}

export interface FilterOptions {
  category?: string;
  minMarketCap?: number;
  maxMarketCap?: number;
  searchTerm?: string;
  sortBy?: 'marketCap' | 'price' | 'change24h' | 'change7d' | 'name';
  sortOrder?: 'asc' | 'desc';
}

export interface ChartDimensions {
  width: number;
  height: number;
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}
