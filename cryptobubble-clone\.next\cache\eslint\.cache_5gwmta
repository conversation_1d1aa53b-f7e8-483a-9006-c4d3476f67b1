[{"C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\app\\layout.tsx": "1", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\app\\page.tsx": "2", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\BubbleChart.tsx": "3", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\CryptoDetailModal.tsx": "4", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\LoadingSpinner.tsx": "5", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\ResponsiveBubbleChart.tsx": "6", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\SearchAndFilter.tsx": "7", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\data\\mockCryptoData.ts": "8", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\types\\index.ts": "9", "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\utils\\formatters.ts": "10"}, {"size": 689, "mtime": 1750138869078, "results": "11", "hashOfConfig": "12"}, {"size": 5771, "mtime": 1750146770445, "results": "13", "hashOfConfig": "12"}, {"size": 10618, "mtime": 1750146685796, "results": "14", "hashOfConfig": "12"}, {"size": 7186, "mtime": 1750140445209, "results": "15", "hashOfConfig": "12"}, {"size": 1198, "mtime": 1750140678712, "results": "16", "hashOfConfig": "12"}, {"size": 1538, "mtime": 1750146669675, "results": "17", "hashOfConfig": "12"}, {"size": 7726, "mtime": 1750146756087, "results": "18", "hashOfConfig": "12"}, {"size": 6700, "mtime": 1750141345232, "results": "19", "hashOfConfig": "12"}, {"size": 833, "mtime": 1750141695582, "results": "20", "hashOfConfig": "12"}, {"size": 1130, "mtime": 1750140667903, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1894bu", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\app\\layout.tsx", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\app\\page.tsx", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\BubbleChart.tsx", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\CryptoDetailModal.tsx", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\ResponsiveBubbleChart.tsx", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\components\\SearchAndFilter.tsx", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\data\\mockCryptoData.ts", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\types\\index.ts", [], [], "C:\\dev\\bubblekrc\\cryptobubble-clone\\src\\utils\\formatters.ts", [], []]