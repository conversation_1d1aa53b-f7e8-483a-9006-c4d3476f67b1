'use client';

import React from 'react';
import { Search, Filter, TrendingUp, TrendingDown } from 'lucide-react';
import { FilterOptions } from '@/types';

interface SearchAndFilterProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  categories: string[];
  compact?: boolean;
}

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  filters,
  onFiltersChange,
  categories,
  compact = false
}) => {
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFiltersChange({ ...filters, searchTerm: e.target.value });
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFiltersChange({ 
      ...filters, 
      category: e.target.value === 'all' ? undefined : e.target.value 
    });
  };

  const handleSortChange = (sortBy: FilterOptions['sortBy']) => {
    const newSortOrder = filters.sortBy === sortBy && filters.sortOrder === 'desc' ? 'asc' : 'desc';
    onFiltersChange({ ...filters, sortBy, sortOrder: newSortOrder });
  };

  if (compact) {
    return (
      <div className="space-y-3">
        {/* Compact Search */}
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-600 w-3 h-3" />
          <input
            type="text"
            placeholder="Search..."
            value={filters.searchTerm || ''}
            onChange={handleSearchChange}
            className="w-full pl-7 pr-3 py-1.5 text-sm text-black font-medium border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent placeholder-gray-600"
          />
        </div>

        {/* Compact Category Filter */}
        <select
          value={filters.category || 'all'}
          onChange={handleCategoryChange}
          className="w-full px-2 py-1.5 text-sm text-black font-medium border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>

        {/* Compact Sort Options */}
        <div className="flex gap-1">
          <button
            onClick={() => handleSortChange('marketCap')}
            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${
              filters.sortBy === 'marketCap'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-black border-gray-300 hover:bg-gray-50'
            }`}
          >
            Cap
          </button>

          <button
            onClick={() => handleSortChange('change24h')}
            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${
              filters.sortBy === 'change24h'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-black border-gray-300 hover:bg-gray-50'
            }`}
          >
            24h
          </button>

          <button
            onClick={() => handleSortChange('price')}
            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${
              filters.sortBy === 'price'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-black border-gray-300 hover:bg-gray-50'
            }`}
          >
            Price
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg border mb-6">
      <div className="flex flex-col lg:flex-row gap-4 items-center">
        {/* Search */}
        <div className="relative flex-1 min-w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 w-4 h-4" />
          <input
            type="text"
            placeholder="Search cryptocurrencies..."
            value={filters.searchTerm || ''}
            onChange={handleSearchChange}
            className="w-full pl-10 pr-4 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-600"
          />
        </div>

        {/* Category Filter */}
        <div className="flex items-center gap-2">
          <Filter className="text-gray-600 w-4 h-4" />
          <select
            value={filters.category || 'all'}
            onChange={handleCategoryChange}
            className="px-3 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {/* Sort Options */}
        <div className="flex gap-2">
          <button
            onClick={() => handleSortChange('marketCap')}
            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${
              filters.sortBy === 'marketCap'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-black border-gray-300 hover:bg-gray-50'
            }`}
          >
            Market Cap
            {filters.sortBy === 'marketCap' && (
              filters.sortOrder === 'desc' ? <TrendingDown className="inline w-4 h-4 ml-1" /> : <TrendingUp className="inline w-4 h-4 ml-1" />
            )}
          </button>

          <button
            onClick={() => handleSortChange('change24h')}
            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${
              filters.sortBy === 'change24h'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-black border-gray-300 hover:bg-gray-50'
            }`}
          >
            24h Change
            {filters.sortBy === 'change24h' && (
              filters.sortOrder === 'desc' ? <TrendingDown className="inline w-4 h-4 ml-1" /> : <TrendingUp className="inline w-4 h-4 ml-1" />
            )}
          </button>

          <button
            onClick={() => handleSortChange('price')}
            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${
              filters.sortBy === 'price'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-black border-gray-300 hover:bg-gray-50'
            }`}
          >
            Price
            {filters.sortBy === 'price' && (
              filters.sortOrder === 'desc' ? <TrendingDown className="inline w-4 h-4 ml-1" /> : <TrendingUp className="inline w-4 h-4 ml-1" />
            )}
          </button>
        </div>
      </div>

      {/* Active Filters Display */}
      {(filters.searchTerm || filters.category) && (
        <div className="mt-4 flex flex-wrap gap-2">
          {filters.searchTerm && (
            <span className="px-3 py-1 bg-blue-100 text-blue-900 rounded-full text-sm font-semibold">
              Search: &ldquo;{filters.searchTerm}&rdquo;
              <button
                onClick={() => onFiltersChange({ ...filters, searchTerm: undefined })}
                className="ml-2 text-blue-700 hover:text-blue-900 font-bold"
              >
                ×
              </button>
            </span>
          )}
          {filters.category && (
            <span className="px-3 py-1 bg-green-100 text-green-900 rounded-full text-sm font-semibold">
              Category: {filters.category}
              <button
                onClick={() => onFiltersChange({ ...filters, category: undefined })}
                className="ml-2 text-green-700 hover:text-green-900 font-bold"
              >
                ×
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};
