<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/2ab49905b99021b2.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-eb71753252589347.js"/><script src="/_next/static/chunks/4bd1b696-299743f5624cdabe.js" async=""></script><script src="/_next/static/chunks/684-3fba02717d6f1d3b.js" async=""></script><script src="/_next/static/chunks/main-app-0cb1f04d80cefe95.js" async=""></script><script src="/_next/static/chunks/415-44580230dfc9c141.js" async=""></script><script src="/_next/static/chunks/app/page-2c791ee06a1ac8cd.js" async=""></script><title>Create Next App</title><meta name="description" content="Generated by create next app"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50"><header class="bg-gradient-to-r from-blue-600 to-purple-700 shadow-lg"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="flex items-center justify-between"><div><h1 class="text-4xl font-bold text-white flex items-center gap-3"><div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center"><span class="text-2xl">₿</span></div>CryptoBubble</h1><p class="text-blue-100 mt-2 text-lg">Interactive cryptocurrency market visualization</p></div><div class="text-right"><div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20"><p class="text-white font-semibold">Loading...</p><p class="text-blue-200 text-sm mt-1">Market data is simulated for demo purposes</p><div class="flex gap-4 mt-2 text-sm text-blue-200"><span>🔍 Search &amp; Filter</span><span>🎯 Click bubbles</span><span>🔍 Zoom &amp; Pan</span></div></div></div></div></div></header><main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="bg-white p-6 rounded-lg shadow-lg border mb-6"><div class="flex flex-col lg:flex-row gap-4 items-center"><div class="relative flex-1 min-w-64"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" aria-hidden="true"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg><input type="text" placeholder="Search cryptocurrencies..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" value=""/></div><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-funnel text-gray-400 w-4 h-4" aria-hidden="true"><path d="M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z"></path></svg><select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"><option value="all" selected="">All Categories</option><option value="DeFi">DeFi</option><option value="Exchange Token">Exchange Token</option><option value="Gaming">Gaming</option><option value="Layer 1">Layer 1</option><option value="Layer 2">Layer 2</option><option value="Meme">Meme</option><option value="NFT">NFT</option><option value="Oracle">Oracle</option><option value="Payment">Payment</option><option value="Privacy">Privacy</option><option value="Smart Contract Platform">Smart Contract Platform</option><option value="Stablecoin">Stablecoin</option><option value="Storage">Storage</option></select></div><div class="flex gap-2"><button class="px-3 py-2 rounded-lg border transition-colors bg-blue-500 text-white border-blue-500">Market Cap<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-down inline w-4 h-4 ml-1" aria-hidden="true"><path d="M16 17h6v-6"></path><path d="m22 17-8.5-8.5-5 5L2 7"></path></svg></button><button class="px-3 py-2 rounded-lg border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">24h Change</button><button class="px-3 py-2 rounded-lg border transition-colors bg-white text-gray-700 border-gray-300 hover:bg-gray-50">Price</button></div></div></div><div class="bg-white rounded-lg shadow-lg p-6"><div class="mb-4"><h2 class="text-xl font-semibold text-gray-900">Market Overview</h2><p class="text-gray-600 text-sm">Bubble size represents market cap • Color indicates 24h price change • Click bubbles for details</p></div><div class="w-full h-[700px] bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg border border-gray-200 flex items-center justify-center"><div class="text-center"><div class="w-16 h-16 animate-spin rounded-full border-4 border-gray-600 border-t-blue-400 mx-auto mb-4"></div><p class="text-white text-lg font-medium">Loading cryptocurrency data...</p><p class="text-gray-400 text-sm mt-2">Preparing interactive bubble chart</p></div></div></div><div class="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6"><div class="lg:col-span-2 bg-white rounded-lg shadow p-6"><h3 class="font-semibold text-gray-900 mb-4">Market Statistics</h3><div class="grid grid-cols-2 md:grid-cols-4 gap-4"><div class="text-center p-3 bg-green-50 rounded-lg"><div class="text-2xl font-bold text-green-600">31</div><div class="text-sm text-gray-600">Gainers (24h)</div></div><div class="text-center p-3 bg-red-50 rounded-lg"><div class="text-2xl font-bold text-red-600">19</div><div class="text-sm text-gray-600">Losers (24h)</div></div><div class="text-center p-3 bg-blue-50 rounded-lg"><div class="text-2xl font-bold text-blue-600">$<!-- -->9.53<!-- -->T</div><div class="text-sm text-gray-600">Total Market Cap</div></div><div class="text-center p-3 bg-purple-50 rounded-lg"><div class="text-2xl font-bold text-purple-600">$<!-- -->1578<!-- -->B</div><div class="text-sm text-gray-600">24h Volume</div></div></div></div><div class="bg-white rounded-lg shadow p-6"><h3 class="font-semibold text-gray-900 mb-4">How to Use</h3><div class="space-y-3 text-sm"><div class="flex items-center gap-3"><div class="w-5 h-5 bg-gradient-to-r from-green-400 to-green-600 rounded-full"></div><span>Green = Price increase (24h)</span></div><div class="flex items-center gap-3"><div class="w-5 h-5 bg-gradient-to-r from-red-400 to-red-600 rounded-full"></div><span>Red = Price decrease (24h)</span></div><div class="flex items-center gap-3"><div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-600 rounded-full"></div><span>Bubble size = Market cap</span></div><div class="pt-2 border-t border-gray-200 space-y-1 text-xs text-gray-600"><div>• Hover bubbles for quick info</div><div>• Click bubbles for detailed view</div><div>• Scroll to zoom, drag to pan</div><div>• Double-click to reset zoom</div></div></div></div></div></main></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-eb71753252589347.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[516,[\"415\",\"static/chunks/415-44580230dfc9c141.js\",\"974\",\"static/chunks/app/page-2c791ee06a1ac8cd.js\"],\"default\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/2ab49905b99021b2.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"7A9j_HO4KoTbLYSMGcSLV\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/2ab49905b99021b2.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"F96B7uZiU0LfIH5Ze5PbP\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Create Next App\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Generated by create next app\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\ne:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>