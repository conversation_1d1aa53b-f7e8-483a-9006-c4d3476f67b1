'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { ResponsiveBubbleChart } from '@/components/ResponsiveBubbleChart';
import { SearchAndFilter } from '@/components/SearchAndFilter';
import { CryptoDetailModal } from '@/components/CryptoDetailModal';
import { getAllCryptoData } from '@/data/mockCryptoData';
import { CryptoCurrency, FilterOptions } from '@/types';

export default function Home() {
  const [selectedCrypto, setSelectedCrypto] = useState<CryptoCurrency | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({
    sortBy: 'marketCap',
    sortOrder: 'desc'
  });

  const allCryptoData = getAllCryptoData();

  // Simulate loading for demo purposes
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Get unique categories for filter dropdown
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(allCryptoData.map(crypto => crypto.category))];
    return uniqueCategories.sort();
  }, [allCryptoData]);

  // Filter and sort data
  const filteredData = useMemo(() => {
    let filtered = allCryptoData;

    // Apply search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(crypto =>
        crypto.name.toLowerCase().includes(searchLower) ||
        crypto.symbol.toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(crypto => crypto.category === filters.category);
    }

    // Apply sorting
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        const aValue = a[filters.sortBy!];
        const bValue = b[filters.sortBy!];

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return filters.sortOrder === 'desc'
            ? bValue.localeCompare(aValue)
            : aValue.localeCompare(bValue);
        }

        return filters.sortOrder === 'desc'
          ? (bValue as number) - (aValue as number)
          : (aValue as number) - (bValue as number);
      });
    }

    return filtered;
  }, [allCryptoData, filters]);

  const handleBubbleClick = (crypto: CryptoCurrency) => {
    setSelectedCrypto(crypto);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedCrypto(null);
  };

  return (
    <div className="w-full h-screen overflow-hidden">
      {/* Full Screen Bubble Chart */}
      <ResponsiveBubbleChart
        data={filteredData}
        onBubbleClick={handleBubbleClick}
        isLoading={isLoading}
      />

      {/* Floating Search and Filter Controls */}
      <div className="fixed top-4 left-4 z-50">
        <div className="bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-4 border border-white/20">
          <SearchAndFilter
            filters={filters}
            onFiltersChange={setFilters}
            categories={categories}
            compact={true}
          />
        </div>
      </div>

      {/* Floating Stats Panel */}
      <div className="fixed top-80 right-4 z-50">
        <div className="bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-4 border border-white/20 min-w-64">
          <h3 className="font-semibold text-gray-900 mb-3">Market Stats</h3>
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="text-center p-2 bg-green-50 rounded">
              <div className="text-lg font-bold text-green-600">
                {filteredData.filter(c => c.change24h > 0).length}
              </div>
              <div className="text-xs text-gray-600">Gainers</div>
            </div>
            <div className="text-center p-2 bg-red-50 rounded">
              <div className="text-lg font-bold text-red-600">
                {filteredData.filter(c => c.change24h < 0).length}
              </div>
              <div className="text-xs text-gray-600">Losers</div>
            </div>
          </div>
          <div className="mt-3 text-xs text-gray-600">
            <div>Total: {filteredData.length} cryptocurrencies</div>
            <div>Market Cap: ${(filteredData.reduce((sum, c) => sum + c.marketCap, 0) / 1e12).toFixed(2)}T</div>
          </div>
        </div>
      </div>

      {/* Floating Legend */}
      <div className="fixed bottom-4 left-4 z-50">
        <div className="bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-4 border border-white/20">
          <h3 className="font-semibold text-gray-900 mb-3 text-sm">Legend</h3>
          <div className="space-y-2 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>Price increase (24h)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span>Price decrease (24h)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
              <span>Bubble size = Market cap</span>
            </div>
            <div className="pt-2 border-t border-gray-200 text-xs text-gray-600">
              <div>• Hover bubbles for details</div>
              <div>• Click bubbles for more info</div>
            </div>
          </div>
        </div>
      </div>

      {/* Detail Modal */}
      <CryptoDetailModal
        crypto={selectedCrypto}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}
