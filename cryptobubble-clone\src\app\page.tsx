'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { ResponsiveBubbleChart } from '@/components/ResponsiveBubbleChart';
import { SearchAndFilter } from '@/components/SearchAndFilter';
import { CryptoDetailModal } from '@/components/CryptoDetailModal';
import { getAllCryptoData } from '@/data/mockCryptoData';
import { CryptoCurrency, FilterOptions } from '@/types';

export default function Home() {
  const [selectedCrypto, setSelectedCrypto] = useState<CryptoCurrency | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({
    sortBy: 'marketCap',
    sortOrder: 'desc'
  });

  const allCryptoData = getAllCryptoData();

  // Simulate loading for demo purposes
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Get unique categories for filter dropdown
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(allCryptoData.map(crypto => crypto.category))];
    return uniqueCategories.sort();
  }, [allCryptoData]);

  // Filter and sort data
  const filteredData = useMemo(() => {
    let filtered = allCryptoData;

    // Apply search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(crypto =>
        crypto.name.toLowerCase().includes(searchLower) ||
        crypto.symbol.toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(crypto => crypto.category === filters.category);
    }

    // Apply sorting
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        const aValue = a[filters.sortBy!];
        const bValue = b[filters.sortBy!];

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return filters.sortOrder === 'desc'
            ? bValue.localeCompare(aValue)
            : aValue.localeCompare(bValue);
        }

        return filters.sortOrder === 'desc'
          ? (bValue as number) - (aValue as number)
          : (aValue as number) - (bValue as number);
      });
    }

    return filtered;
  }, [allCryptoData, filters]);

  const handleBubbleClick = (crypto: CryptoCurrency) => {
    setSelectedCrypto(crypto);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedCrypto(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-blue-600 to-purple-700 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-2xl">₿</span>
                </div>
                CryptoBubble
              </h1>
              <p className="text-blue-100 mt-2 text-lg">Interactive cryptocurrency market visualization</p>
            </div>
            <div className="text-right">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <p className="text-white font-semibold">
                  {isLoading ? 'Loading...' : `${filteredData.length} cryptocurrencies`}
                </p>
                <p className="text-blue-200 text-sm mt-1">Market data is simulated for demo purposes</p>
                <div className="flex gap-4 mt-2 text-sm text-blue-200">
                  <span>🔍 Search & Filter</span>
                  <span>🎯 Click bubbles</span>
                  <span>🔍 Zoom & Pan</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filter */}
        <SearchAndFilter
          filters={filters}
          onFiltersChange={setFilters}
          categories={categories}
        />

        {/* Bubble Chart */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Market Overview</h2>
            <p className="text-gray-600 text-sm">
              Bubble size represents market cap • Color indicates 24h price change • Click bubbles for details
            </p>
          </div>

          <ResponsiveBubbleChart
            data={filteredData}
            onBubbleClick={handleBubbleClick}
            isLoading={isLoading}
          />
        </div>

        {/* Statistics and Legend */}
        <div className="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Market Statistics */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Market Statistics</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {filteredData.filter(c => c.change24h > 0).length}
                </div>
                <div className="text-sm text-gray-600">Gainers (24h)</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {filteredData.filter(c => c.change24h < 0).length}
                </div>
                <div className="text-sm text-gray-600">Losers (24h)</div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  ${(filteredData.reduce((sum, c) => sum + c.marketCap, 0) / 1e12).toFixed(2)}T
                </div>
                <div className="text-sm text-gray-600">Total Market Cap</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  ${(filteredData.reduce((sum, c) => sum + c.volume24h, 0) / 1e9).toFixed(0)}B
                </div>
                <div className="text-sm text-gray-600">24h Volume</div>
              </div>
            </div>
          </div>

          {/* Legend */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="font-semibold text-gray-900 mb-4">How to Use</h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 bg-gradient-to-r from-green-400 to-green-600 rounded-full"></div>
                <span>Green = Price increase (24h)</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 bg-gradient-to-r from-red-400 to-red-600 rounded-full"></div>
                <span>Red = Price decrease (24h)</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-600 rounded-full"></div>
                <span>Bubble size = Market cap</span>
              </div>
              <div className="pt-2 border-t border-gray-200 space-y-1 text-xs text-gray-600">
                <div>• Hover bubbles for quick info</div>
                <div>• Click bubbles for detailed view</div>
                <div>• Scroll to zoom, drag to pan</div>
                <div>• Double-click to reset zoom</div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Detail Modal */}
      <CryptoDetailModal
        crypto={selectedCrypto}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}
