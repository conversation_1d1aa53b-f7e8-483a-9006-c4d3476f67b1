# CryptoBubble Clone

A modern, interactive cryptocurrency market visualization inspired by CryptoBubble.net. This application displays cryptocurrency data in an engaging bubble chart format where bubble size represents market cap and color indicates price changes.

![CryptoBubble Clone](https://img.shields.io/badge/Next.js-15.3.3-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![D3.js](https://img.shields.io/badge/D3.js-7.0-orange?style=for-the-badge&logo=d3.js)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.0-38B2AC?style=for-the-badge&logo=tailwind-css)

## ✨ Features

- **Interactive Bubble Chart**: Visualize cryptocurrency market data with D3.js-powered bubble charts
- **Real-time Interactions**: Hover effects, click handlers, and smooth animations
- **Responsive Design**: Fully responsive layout that works on all device sizes
- **Search & Filter**: Find specific cryptocurrencies and filter by categories
- **Detailed Modal Views**: Click bubbles to see comprehensive cryptocurrency information
- **Market Statistics**: Real-time market overview with gainers, losers, and total market cap
- **Modern UI**: Beautiful gradient backgrounds, shadows, and polished components
- **Performance Optimized**: Built with Next.js 15 and optimized for smooth animations

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd cryptobubble-clone
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🛠️ Built With

- **[Next.js 15](https://nextjs.org/)** - React framework with App Router
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript
- **[D3.js](https://d3js.org/)** - Data visualization and bubble chart creation
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **[Framer Motion](https://www.framer.com/motion/)** - Animation library
- **[Lucide React](https://lucide.dev/)** - Beautiful icons

## 📊 How It Works

### Bubble Chart Visualization
- **Bubble Size**: Represents market capitalization (larger = higher market cap)
- **Bubble Color**: Indicates 24-hour price change (green = positive, red = negative)
- **Interactive Elements**: Hover for quick info, click for detailed view
- **Zoom & Pan**: Scroll to zoom, drag to pan around the chart
- **Force Simulation**: Uses D3.js physics simulation for natural bubble positioning

### Data Structure
The application uses mock cryptocurrency data with the following structure:
```typescript
interface CryptoCurrency {
  id: string;
  name: string;
  symbol: string;
  price: number;
  marketCap: number;
  volume24h: number;
  change24h: number;
  change7d: number;
  rank: number;
  category: string;
  description?: string;
}
```

## 🎯 Key Components

- **`BubbleChart`**: Core D3.js visualization component
- **`ResponsiveBubbleChart`**: Responsive wrapper with auto-sizing
- **`SearchAndFilter`**: Search and filtering functionality
- **`CryptoDetailModal`**: Detailed cryptocurrency information modal
- **`LoadingSpinner`**: Loading states and skeleton screens

## 🔧 Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript type checking
```

## 📱 Features in Detail

### Interactive Bubble Chart
- Smooth entrance animations for bubbles
- Hover effects with scaling and color changes
- Click animations with visual feedback
- Responsive tooltip with comprehensive data
- Zoom and pan capabilities
- Double-click to reset zoom

### Search and Filtering
- Real-time search by cryptocurrency name or symbol
- Filter by category (DeFi, Layer 1, Meme, etc.)
- Sort by market cap, price, or 24h change
- Visual indicators for active filters

### Market Statistics
- Live count of gainers vs losers
- Total market capitalization
- 24-hour trading volume
- Category distribution

### Responsive Design
- Mobile-first approach
- Adaptive chart sizing
- Touch-friendly interactions
- Optimized for all screen sizes

## 🎨 Design Philosophy

The application follows modern web design principles:
- **Clean & Minimal**: Focus on data visualization
- **Accessible**: Proper contrast ratios and keyboard navigation
- **Performance**: Optimized animations and efficient rendering
- **User Experience**: Intuitive interactions and clear feedback

## 🚀 Future Enhancements

- [ ] Real cryptocurrency API integration
- [ ] Historical price charts in detail modal
- [ ] Portfolio tracking functionality
- [ ] Price alerts and notifications
- [ ] Dark/light theme toggle
- [ ] Export functionality for charts
- [ ] Advanced filtering options
- [ ] WebSocket real-time updates

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you have any questions or need help, please open an issue on GitHub.

---

**Note**: This application uses simulated cryptocurrency data for demonstration purposes. For production use, integrate with real cryptocurrency APIs like CoinGecko or CoinMarketCap.
